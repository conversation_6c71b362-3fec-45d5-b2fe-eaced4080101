using Domain.Entities;
using Infrastructure.ExpressionEngine;
using Infrastructure.ExpressionEngine.Exceptions;
using Infrastructure.ExpressionEngine.Services;
using Infrastructure.Tests.RuleEngine;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Infrastructure.Tests.Integration;

/// <summary>
/// Komplexní testy pro celý workflow RuleEngine systému.
/// Testuje reálné scénáře s objednávkami a fakturami.
/// </summary>
public class CompleteRuleEngineWorkflowTests
{
    private readonly CalculationEngine _engine;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;

    public CompleteRuleEngineWorkflowTests()
    {
        _entityTypeMap = new Dictionary<string, Type>
        {
            ["Order"] = typeof(Order),
            ["OrderItem"] = typeof(OrderItem),
            ["Invoice"] = typeof(Invoice),
            ["InvoiceItem"] = typeof(InvoiceItem)
        };

        var dataProvider = new TestRuleDataProvider();
        var expressionBuilder = TestExpressionBuilderFactory.Create(dataProvider, _entityTypeMap);
        var mockLogger = new Mock<ILogger<CalculationEngine>>();
        _engine = new CalculationEngine(expressionBuilder, _entityTypeMap, mockLogger.Object);
    }

    [Fact]
    public void CompleteWorkflow_OrderDiscountRule_WorksCorrectly()
    {
        // Arrange - Vytvoříme pravidlo pro slevu 10% nad 20 000 Kč
        var discountRule = CreateDiscountRule();
        
        // Testovací objednávky
        var smallOrder = CreateOrder(15000m); // Pod limitem
        var largeOrder = CreateOrder(25000m); // Nad limitem

        // Act - Vykonáme pravidlo na obou objednávkách
        var smallOrderDiscount = _engine.Execute(discountRule, smallOrder);
        var largeOrderDiscount = _engine.Execute(discountRule, largeOrder);

        // Assert
        Assert.Equal(0m, smallOrderDiscount); // Malá objednávka - žádná sleva
        Assert.Equal(2500m, largeOrderDiscount); // Velká objednávka - 10% sleva
    }

    [Fact]
    public void CompleteWorkflow_ShippingRule_WorksCorrectly()
    {
        // Arrange - Vytvoříme pravidlo pro dopravu zdarma nad 15 000 Kč
        var shippingRule = CreateShippingRule();
        
        var smallOrder = CreateOrder(12000m); // Pod limitem
        var mediumOrder = CreateOrder(18000m); // Nad limitem

        // Act
        var smallOrderShipping = _engine.Execute(shippingRule, smallOrder);
        var mediumOrderShipping = _engine.Execute(shippingRule, mediumOrder);

        // Assert
        Assert.Equal(200m, smallOrderShipping); // Malá objednávka - standardní poštovné
        Assert.Equal(0m, mediumOrderShipping); // Střední objednávka - doprava zdarma
    }

    [Fact]
    public void CompleteWorkflow_InvoiceReminderRule_WorksCorrectly()
    {
        // Arrange - Vytvoříme pravidlo pro upomínky
        var reminderRule = CreateReminderRule();
        
        var currentInvoice = CreateInvoice(false, 3); // Aktuální faktura
        var overdueInvoice = CreateInvoice(true, 10); // Po splatnosti více než 7 dní
        var recentOverdueInvoice = CreateInvoice(true, 5); // Po splatnosti méně než 7 dní

        // Act
        var currentInvoiceReminder = _engine.Execute(reminderRule, currentInvoice);
        var overdueInvoiceReminder = _engine.Execute(reminderRule, overdueInvoice);
        var recentOverdueReminder = _engine.Execute(reminderRule, recentOverdueInvoice);

        // Assert
        Assert.False((bool)currentInvoiceReminder); // Aktuální faktura - bez upomínky
        Assert.True((bool)overdueInvoiceReminder); // Dlouho po splatnosti - s upomínkou
        Assert.False((bool)recentOverdueReminder); // Krátce po splatnosti - bez upomínky
    }

    [Fact]
    public void CompleteWorkflow_MultipleRulesOnSameEntity_WorksCorrectly()
    {
        // Arrange - Aplikujeme více pravidel na stejnou objednávku
        var discountRule = CreateDiscountRule();
        var shippingRule = CreateShippingRule();
        
        var order = CreateOrder(22000m); // Nad oběma limity

        // Act
        var discount = _engine.Execute(discountRule, order);
        var shipping = _engine.Execute(shippingRule, order);

        // Assert
        Assert.Equal(2200m, discount); // 10% sleva z 22 000 Kč
        Assert.Equal(0m, shipping); // Doprava zdarma
        
        // Ověříme, že celková úspora je správná
        var totalSavings = (decimal)discount + 200m; // Sleva + ušetřené poštovné
        Assert.Equal(2400m, totalSavings);
    }

    [Fact]
    public void CompleteWorkflow_ComplexBusinessScenario_WorksCorrectly()
    {
        // Arrange - Komplexní obchodní scénář
        // Velká objednávka s více položkami
        var order = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = "ORD-COMPLEX-001",
            OrderDate = DateTime.Now.AddDays(-5),
            CustomerName = "VIP Zákazník",
            CustomerEmail = "<EMAIL>",
            Status = OrderStatus.Processing,
            SubTotal = 45000m,
            TaxAmount = 9450m,
            TotalAmount = 54450m,
            Currency = "CZK"
        };

        order.Items = new List<OrderItem>
        {
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order.Id,
                ProductCode = "LAPTOP-PRO",
                ProductName = "MacBook Pro 16\"",
                Category = "Elektronika",
                UnitPrice = 80000m,
                Quantity = 1,
                TaxRate = 21m,
                LineTotal = 80000m,
                LineTaxAmount = 16800m,
                LineTotalWithTax = 96800m
            }
        };

        var discountRule = CreateDiscountRule();
        var shippingRule = CreateShippingRule();

        // Act
        var discount = _engine.Execute(discountRule, order);
        var shipping = _engine.Execute(shippingRule, order);

        // Assert
        Assert.Equal(5445m, discount); // 10% z 54 450 Kč
        Assert.Equal(0m, shipping); // Doprava zdarma
        Assert.True(order.TotalItemCount > 0); // Má položky
        Assert.True(order.TotalAmount > 50000m); // Je to velká objednávka
    }

    [Fact]
    public void CompleteWorkflow_RuleValidation_WorksCorrectly()
    {
        // Arrange - Testujeme validaci pravidel
        var validRule = CreateDiscountRule();
        var invalidRule = new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Neplatné pravidlo",
            TargetEntityName = "NonExistentEntity", // Neexistující entita
            RootNode = new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.String, Value = "test" }
        };

        // Act
        var validResult = _engine.ValidateExpression(validRule);
        var invalidResult = _engine.ValidateExpression(invalidRule);

        // Assert
        Assert.True(validResult.IsValid);
        Assert.False(invalidResult.IsValid);
        Assert.Contains("Neznámá cílová entita", invalidResult.ErrorMessage);
    }

    // Helper metody pro vytváření testovacích objektů

    private Expression CreateDiscountRule()
    {
        return new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Sleva 10% nad 20 000 Kč",
            TargetEntityName = "Order",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<ExpressionNode>
                {
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<ExpressionNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.Decimal, Value = "20000" }
                        }
                    },
                    new OperationNode
                    {
                        Operator = OperatorType.Multiply,
                        Operands = new List<ExpressionNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.Decimal, Value = "0.1" }
                        }
                    },
                    new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.Decimal, Value = "0" }
                }
            }
        };
    }

    private Expression CreateShippingRule()
    {
        return new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Doprava zdarma nad 15 000 Kč",
            TargetEntityName = "Order",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<ExpressionNode>
                {
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<ExpressionNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.Decimal, Value = "15000" }
                        }
                    },
                    new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.Decimal, Value = "0" },
                    new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.Decimal, Value = "200" }
                }
            }
        };
    }

    private Expression CreateReminderRule()
    {
        return new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Upomínka po splatnosti",
            TargetEntityName = "Invoice",
            RootNode = new OperationNode
            {
                Operator = OperatorType.And,
                Operands = new List<ExpressionNode>
                {
                    new SourceValueNode { SourcePath = "IsOverdue" },
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<ExpressionNode>
                        {
                            new SourceValueNode { SourcePath = "DaysOverdue" },
                            new ConstantNode { DataType = Infrastructure.ExpressionEngine.ValueType.Integer, Value = "7" }
                        }
                    }
                }
            }
        };
    }

    private Order CreateOrder(decimal totalAmount)
    {
        return new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = $"TEST-{totalAmount}",
            OrderDate = DateTime.Now.AddDays(-2),
            CustomerName = "Test Customer",
            CustomerEmail = "<EMAIL>",
            Status = OrderStatus.Processing,
            SubTotal = totalAmount * 0.826m, // Přibližně bez DPH
            TaxAmount = totalAmount * 0.174m, // Přibližně DPH
            TotalAmount = totalAmount,
            Currency = "CZK",
            Items = new List<OrderItem>
            {
                new OrderItem
                {
                    Id = Guid.NewGuid(),
                    ProductCode = "TEST-PRODUCT",
                    ProductName = "Test Product",
                    Category = "Test",
                    UnitPrice = totalAmount * 0.826m,
                    Quantity = 1,
                    TaxRate = 21m,
                    LineTotal = totalAmount * 0.826m,
                    LineTaxAmount = totalAmount * 0.174m,
                    LineTotalWithTax = totalAmount
                }
            }
        };
    }

    private Invoice CreateInvoice(bool isOverdue, int daysOverdue)
    {
        var dueDate = isOverdue ? DateTime.Now.AddDays(-daysOverdue) : DateTime.Now.AddDays(14);
        
        return new Invoice
        {
            Id = Guid.NewGuid(),
            InvoiceNumber = $"INV-{daysOverdue}",
            IssueDate = DateTime.Now.AddDays(-30),
            DueDate = dueDate,
            CustomerName = "Test Customer",
            CustomerEmail = "<EMAIL>",
            Type = InvoiceType.Standard,
            Status = isOverdue ? InvoiceStatus.Overdue : InvoiceStatus.Issued,
            SubTotal = 10000m,
            TaxAmount = 2100m,
            TotalAmount = 12100m,
            PaidAmount = 0m,
            RemainingAmount = 12100m,
            Currency = "CZK",
            PaymentMethod = PaymentMethod.BankTransfer
        };
    }
}
