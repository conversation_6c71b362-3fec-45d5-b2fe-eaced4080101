using System.Linq.Expressions;
using System.Reflection;
using Infrastructure.ExpressionEngine;
using Infrastructure.ExpressionEngine.Exceptions;
using Infrastructure.ExpressionEngine.Builders;

namespace Infrastructure.Tests.RuleEngine;

/// <summary>
/// Factory pro vytváření ExpressionBuilder instancí v testech.
/// Zapouzdřuje složitost vytváření všech potřebných builderů.
/// </summary>
public static class TestExpressionBuilderFactory
{
    /// <summary>
    /// Vytvoří ExpressionBuilder s výchozími buildery pro testování.
    /// </summary>
    /// <param name="dataProvider">Poskytovatel dat pro lookup a související agregace</param>
    /// <param name="entityTypeMap">Mapa názvů entit na jejich typy</param>
    /// <param name="propertyMetadata">Volitelná metadata vlastností</param>
    /// <param name="logHook">Volitelný hook pro logování</param>
    /// <returns>Nakonfigurovaný ExpressionBuilder</returns>
    public static ExpressionBuilder Create(
        IExpressionDataProvider dataProvider,
        IReadOnlyDictionary<string, Type> entityTypeMap,
        IReadOnlyDictionary<string, PropertyInfo>? propertyMetadata = null,
        Action<ExpressionNode, System.Linq.Expressions.Expression>? logHook = null)
    {
        var constantBuilder = new ConstantExpressionBuilder();
        var sourceBuilder = new SourceExpressionBuilder(propertyMetadata);
        var operationBuilder = new OperationExpressionBuilder();
        var lookupBuilder = new LookupExpressionBuilder(dataProvider, entityTypeMap);
        var aggregationBuilder = new AggregationExpressionBuilder(dataProvider, entityTypeMap);

        return new ExpressionBuilder(
            constantBuilder,
            sourceBuilder,
            operationBuilder,
            lookupBuilder,
            aggregationBuilder,
            logHook);
    }
}
