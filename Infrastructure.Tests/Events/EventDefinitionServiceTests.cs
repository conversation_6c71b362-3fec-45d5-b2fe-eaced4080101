using Domain.Entities;
using Infrastructure.Events;
using Infrastructure.Events.Abstraction;
using Infrastructure.Events.Persistence;
using Infrastructure.Events.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SharedKernel.Domain;
using Xunit;

namespace Infrastructure.Tests.Events;

/// <summary>
/// Testy pro EventDefinitionService.
/// </summary>
public class EventDefinitionServiceTests : IDisposable
{
    private readonly EventsDbContext _context;
    private readonly EventDefinitionService _service;
    private readonly IServiceProvider _serviceProvider;

    public EventDefinitionServiceTests()
    {
        var options = new DbContextOptionsBuilder<EventsDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new EventsDbContext(options);

        // Vytvoříme mock service provider
        var services = new ServiceCollection();
        services.AddLogging();
        _serviceProvider = services.BuildServiceProvider();

        var logger = _serviceProvider.GetRequiredService<ILogger<EventDefinitionService>>();
        _service = new EventDefinitionService(_context, logger, _serviceProvider);
    }

    [Fact]
    public async Task CreateAsync_ShouldCreateEventDefinition()
    {
        // Arrange
        var definition = new EventDefinition(
            "TestEvent", 
            typeof(SampleEntity), 
            EventType.Created)
        {
            Description = "Test event description"
        };

        // Act
        var result = await _service.CreateAsync(definition);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.Equal("TestEvent", result.EventName);
        Assert.Equal(typeof(SampleEntity).AssemblyQualifiedName, result.EntityTypeName);
        Assert.Equal(EventType.Created, result.Operation);
        Assert.Equal("Test event description", result.Description);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetByNameAsync_ShouldReturnEventDefinition()
    {
        // Arrange
        var definition = new EventDefinition(
            "TestEvent", 
            typeof(SampleEntity), 
            EventType.Updated);
        await _service.CreateAsync(definition);

        // Act
        var result = await _service.GetByNameAsync("TestEvent");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestEvent", result.EventName);
        Assert.Equal(EventType.Updated, result.Operation);
    }

    [Fact]
    public async Task GetByNameAsync_WithNonExistentName_ShouldReturnNull()
    {
        // Act
        var result = await _service.GetByNameAsync("NonExistentEvent");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetActiveAsync_ShouldReturnOnlyActiveDefinitions()
    {
        // Arrange
        var activeDefinition = new EventDefinition(
            "ActiveEvent", 
            typeof(SampleEntity), 
            EventType.Created)
        {
            IsActive = true
        };
        
        var inactiveDefinition = new EventDefinition(
            "InactiveEvent", 
            typeof(SampleEntity), 
            EventType.Deleted)
        {
            IsActive = false
        };

        await _service.CreateAsync(activeDefinition);
        await _service.CreateAsync(inactiveDefinition);

        // Act
        var result = await _service.GetActiveAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal("ActiveEvent", result.First().EventName);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEventDefinition()
    {
        // Arrange
        var definition = new EventDefinition(
            "TestEvent", 
            typeof(SampleEntity), 
            EventType.Created);
        var created = await _service.CreateAsync(definition);

        // Act
        created.Description = "Updated description";
        created.IsActive = false;
        var updated = await _service.UpdateAsync(created);

        // Assert
        Assert.Equal("Updated description", updated.Description);
        Assert.False(updated.IsActive);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEventDefinition()
    {
        // Arrange
        var definition = new EventDefinition(
            "TestEvent", 
            typeof(SampleEntity), 
            EventType.Created);
        var created = await _service.CreateAsync(definition);

        // Act
        var result = await _service.DeleteAsync(created.Id);

        // Assert
        Assert.True(result);
        
        var deleted = await _service.GetByIdAsync(created.Id);
        Assert.Null(deleted);
    }

    [Fact]
    public async Task TrackedProperties_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var definition = new EventDefinition(
            "TestEvent", 
            typeof(SampleEntity), 
            EventType.Updated);
        
        definition.TrackedProperties = new List<string> { "Name", "Age", "Description" };

        // Act
        var created = await _service.CreateAsync(definition);
        var retrieved = await _service.GetByIdAsync(created.Id);

        // Assert
        Assert.NotNull(retrieved);
        Assert.Equal(3, retrieved.TrackedProperties.Count);
        Assert.Contains("Name", retrieved.TrackedProperties);
        Assert.Contains("Age", retrieved.TrackedProperties);
        Assert.Contains("Description", retrieved.TrackedProperties);
    }

    [Fact]
    public async Task EntityType_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var definition = new EventDefinition(
            "TestEvent", 
            typeof(SampleEntity), 
            EventType.Created);

        // Act
        var created = await _service.CreateAsync(definition);
        var retrieved = await _service.GetByIdAsync(created.Id);

        // Assert
        Assert.NotNull(retrieved);
        Assert.Equal(typeof(SampleEntity), retrieved.EntityType);
        Assert.Equal("SampleEntity", retrieved.EntityName);
    }

    public void Dispose()
    {
        _context.Dispose();
        _serviceProvider.GetService<IServiceScope>()?.Dispose();
    }
}
