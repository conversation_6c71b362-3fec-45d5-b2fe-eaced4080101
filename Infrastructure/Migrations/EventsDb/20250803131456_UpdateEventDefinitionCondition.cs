using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations.EventsDb
{
    /// <inheritdoc />
    public partial class UpdateEventDefinitionCondition : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConditionExpression",
                table: "EventDefinitions");

            migrationBuilder.AddColumn<string>(
                name: "ConditionExpressionName",
                table: "EventDefinitions",
                type: "TEXT",
                maxLength: 200,
                nullable: true,
                comment: "Název výrazu z Expression Engine pro podmínku spuštění události");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConditionExpressionName",
                table: "EventDefinitions");

            migrationBuilder.AddColumn<string>(
                name: "ConditionExpression",
                table: "EventDefinitions",
                type: "TEXT",
                maxLength: 1000,
                nullable: true,
                comment: "Podmínka pro spuštění události jako string výraz");
        }
    }
}
