// <auto-generated />
using System;
using Infrastructure.Events.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Infrastructure.Migrations.EventsDb
{
    [DbContext(typeof(EventsDbContext))]
    [Migration("20250803111644_AddEventDefinitions")]
    partial class AddEventDefinitions
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("Infrastructure.Events.EventDefinition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConditionExpression")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Podmínka pro spuštění události jako string výraz");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasComment("Datum a čas vytvoření definice");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Popis události pro dokumentační účely");

                    b.Property<string>("EntityTypeName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("Plný název typu entity včetně namespace");

                    b.Property<string>("EventName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Jedinečný název události");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true)
                        .HasComment("Indikuje, zda je definice aktivní");

                    b.Property<string>("Operation")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Typ operace při které se událost spouští");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("BLOB")
                        .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 })
                        .HasComment("Verze řádku pro optimistické zamykání");

                    b.Property<string>("TrackedPropertiesJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("[]")
                        .HasComment("Seznam sledovaných vlastností serializovaný jako JSON");

                    b.HasKey("Id");

                    b.HasIndex("EntityTypeName")
                        .HasDatabaseName("IX_EventDefinitions_EntityTypeName");

                    b.HasIndex("EventName")
                        .IsUnique()
                        .HasDatabaseName("IX_EventDefinitions_EventName_Unique");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_EventDefinitions_IsActive");

                    b.HasIndex("EntityTypeName", "Operation")
                        .HasDatabaseName("IX_EventDefinitions_EntityTypeName_Operation");

                    b.ToTable("EventDefinitions", null, t =>
                        {
                            t.HasCheckConstraint("CK_EventDefinitions_EntityTypeName_NotEmpty", "[EntityTypeName] IS NOT NULL AND LENGTH(TRIM([EntityTypeName])) > 0");

                            t.HasCheckConstraint("CK_EventDefinitions_EventName_NotEmpty", "[EventName] IS NOT NULL AND LENGTH(TRIM([EventName])) > 0");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
