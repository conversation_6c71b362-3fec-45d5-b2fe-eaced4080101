using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations.EventsDb
{
    /// <inheritdoc />
    public partial class AddEventDefinitions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EventDefinitions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EventName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "<PERSON><PERSON><PERSON><PERSON><PERSON> název události"),
                    EntityTypeName = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false, comment: "Plný název typu entity včetně namespace"),
                    Operation = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "Typ operace při které se událost spouští"),
                    TrackedPropertiesJson = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: false, defaultValue: "[]", comment: "Seznam sledovaných vlastností serializovaný jako JSON"),
                    ConditionExpression = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "Podmínka pro spuštění události jako string výraz"),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "Popis události pro dokumentační účely"),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true, comment: "Indikuje, zda je definice aktivní"),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false, comment: "Datum a čas vytvoření definice"),
                    RowVersion = table.Column<byte[]>(type: "BLOB", rowVersion: true, nullable: false, defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 }, comment: "Verze řádku pro optimistické zamykání")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EventDefinitions", x => x.Id);
                    table.CheckConstraint("CK_EventDefinitions_EntityTypeName_NotEmpty", "[EntityTypeName] IS NOT NULL AND LENGTH(TRIM([EntityTypeName])) > 0");
                    table.CheckConstraint("CK_EventDefinitions_EventName_NotEmpty", "[EventName] IS NOT NULL AND LENGTH(TRIM([EventName])) > 0");
                });

            migrationBuilder.CreateIndex(
                name: "IX_EventDefinitions_EntityTypeName",
                table: "EventDefinitions",
                column: "EntityTypeName");

            migrationBuilder.CreateIndex(
                name: "IX_EventDefinitions_EntityTypeName_Operation",
                table: "EventDefinitions",
                columns: new[] { "EntityTypeName", "Operation" });

            migrationBuilder.CreateIndex(
                name: "IX_EventDefinitions_EventName_Unique",
                table: "EventDefinitions",
                column: "EventName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EventDefinitions_IsActive",
                table: "EventDefinitions",
                column: "IsActive");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EventDefinitions");
        }
    }
}
