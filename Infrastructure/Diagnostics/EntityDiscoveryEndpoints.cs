using Infrastructure.RuleEngine.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace Infrastructure.Diagnostics;

/// <summary>
/// Diagnostické endpointy pro ověření automatického zjišťování entit
/// </summary>
public static class EntityDiscoveryEndpoints
{
    /// <summary>
    /// Mapuje diagnostické endpointy pro entity discovery
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication MapEntityDiscoveryDiagnostics(this WebApplication app)
    {
        var group = app.MapGroup("/diagnostics/entities")
            .WithTags("Diagnostics");

        group.MapGet("/discovery", GetEntityDiscoveryDiagnostics)
            .WithName("GetEntityDiscoveryDiagnostics")
            .WithSummary("Diagnostika automatického zjiš<PERSON>ování entit")
            .WithDescription("Vrací informace o tom, zda automatické zjišťování entit funguje správně");

        group.MapGet("/registered", GetRegisteredEntities)
            .WithName("GetRegisteredEntities")
            .WithSummary("Seznam registrovaných entit")
            .WithDescription("Vrací seznam všech aktuálně registrovaných entit v systému");

        group.MapGet("/metadata", GetEntityMetadata)
            .WithName("GetEntityMetadata")
            .WithSummary("Metadata entit pro RuleEngine")
            .WithDescription("Vrací metadata všech entit dostupných v RuleEngine včetně jejich vlastností");

        return app;
    }

    /// <summary>
    /// Endpoint pro diagnostiku automatického zjišťování entit
    /// </summary>
    private static async Task<IResult> GetEntityDiscoveryDiagnostics()
    {
        var (isWorking, discoveredEntities, missingDtoTypes) = 
            Application.DependencyInjection.DiagnoseEntityDiscovery();

        var result = new
        {
            AutoDiscoveryWorking = isWorking,
            DiscoveredEntitiesCount = discoveredEntities.Count,
            DiscoveredEntities = discoveredEntities,
            MissingDtoTypesCount = missingDtoTypes.Count,
            MissingDtoTypes = missingDtoTypes,
            Recommendation = isWorking 
                ? "Automatické zjišťování entit funguje správně." 
                : "Automatické zjišťování entit nefunguje. Používá se manuální registrace."
        };

        return Results.Ok(result);
    }

    /// <summary>
    /// Endpoint pro zobrazení všech registrovaných entit
    /// </summary>
    private static async Task<IResult> GetRegisteredEntities()
    {
        var entityTypes = Application.DependencyInjection.GetEntityTypesForInfrastructure();
        
        var entities = entityTypes.Select(entityInfo =>
        {
            var entityInfoType = entityInfo.GetType();
            var entityType = (Type)entityInfoType.GetProperty("EntityType")!.GetValue(entityInfo)!;
            var keyType = (Type)entityInfoType.GetProperty("KeyType")!.GetValue(entityInfo)!;
            var dtoType = (Type)entityInfoType.GetProperty("DtoType")!.GetValue(entityInfo)!;
            var addEditType = (Type)entityInfoType.GetProperty("AddEditType")!.GetValue(entityInfo)!;

            return new
            {
                EntityName = entityType.Name,
                EntityFullName = entityType.FullName,
                KeyType = keyType.Name,
                DtoType = dtoType.Name,
                AddEditType = addEditType.Name,
                Assembly = entityType.Assembly.GetName().Name
            };
        }).ToList();

        var result = new
        {
            TotalEntities = entities.Count,
            Entities = entities
        };

        return Results.Ok(result);
    }

    /// <summary>
    /// Endpoint pro zobrazení metadat entit pro RuleEngine
    /// </summary>
    private static async Task<IResult> GetEntityMetadata([FromServices] IEntityMetadataService metadataService)
    {
        try
        {
            var entities = metadataService.GetAvailableEntities();

            var entitiesWithProperties = entities.Select(entity => new
            {
                entity.Name,
                entity.DisplayName,
                entity.Description,
                Properties = metadataService.GetEntityProperties(entity.Name),
                PropertyCount = metadataService.GetEntityProperties(entity.Name).Count
            }).ToList();

            var result = new
            {
                TotalEntities = entities.Count,
                Entities = entitiesWithProperties,
                GeneratedAt = DateTime.UtcNow,
                Note = "Metadata jsou generována dynamicky z registrovaných entit"
            };

            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Chyba při načítání metadat entit: {ex.Message}");
        }
    }
}
