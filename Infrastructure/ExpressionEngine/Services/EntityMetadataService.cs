using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Infrastructure.RuleEngine.API;
using SharedKernel.Domain;

namespace Infrastructure.RuleEngine.Services;

/// <summary>
/// Služba pro dynamické generování metadat entit pro RuleEngine
/// </summary>
public interface IEntityMetadataService
{
    /// <summary>
    /// Získá metadata všech dostupných entit
    /// </summary>
    /// <returns>Seznam metadat entit</returns>
    List<EntityMetadata> GetAvailableEntities();

    /// <summary>
    /// Získá vlastnosti konkrétní entity
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <returns>Seznam vlastností entity</returns>
    List<PropertyMetadata> GetEntityProperties(string entityName);

    /// <summary>
    /// Ověří, zda je entita podporována
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <returns>True pokud je entita podporována</returns>
    bool IsEntitySupported(string entityName);
}

/// <summary>
/// Implementace služby pro metadata entit
/// </summary>
public class EntityMetadataService : IEntityMetadataService
{
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;
    private readonly Dictionary<string, EntityDisplayInfo> _displayInfoMap;

    public EntityMetadataService(IReadOnlyDictionary<string, Type> entityTypeMap)
    {
        _entityTypeMap = entityTypeMap;
        _displayInfoMap = InitializeDisplayInfoMap();
    }

    public List<EntityMetadata> GetAvailableEntities()
    {
        var entities = new List<EntityMetadata>();

        foreach (var kvp in _entityTypeMap)
        {
            var entityName = kvp.Key;
            var entityType = kvp.Value;

            // Získáme display informace (buď z konfigurace nebo automaticky)
            var displayInfo = GetDisplayInfo(entityName, entityType);

            entities.Add(new EntityMetadata
            {
                Name = entityName,
                DisplayName = displayInfo.DisplayName,
                Description = displayInfo.Description
            });
        }

        return entities.OrderBy(e => e.DisplayName).ToList();
    }

    public List<PropertyMetadata> GetEntityProperties(string entityName)
    {
        if (!_entityTypeMap.TryGetValue(entityName, out var entityType))
        {
            return new List<PropertyMetadata>();
        }

        return GeneratePropertyMetadata(entityType);
    }

    public bool IsEntitySupported(string entityName)
    {
        return !string.IsNullOrWhiteSpace(entityName) && _entityTypeMap.ContainsKey(entityName);
    }

    /// <summary>
    /// Inicializuje mapu s display informacemi pro entity
    /// </summary>
    private Dictionary<string, EntityDisplayInfo> InitializeDisplayInfoMap()
    {
        return new Dictionary<string, EntityDisplayInfo>
        {
            // Obchodní entity
            ["Order"] = new("Objednávka", "Objednávka zákazníka s položkami a dodacími údaji"),
            ["OrderItem"] = new("Položka objednávky", "Jednotlivá položka v objednávce"),
            ["Invoice"] = new("Faktura", "Faktura vystavená zákazníkovi"),
            ["InvoiceItem"] = new("Položka faktury", "Jednotlivá položka na faktuře"),
            
            // Ukázkové entity
            ["SampleEntity"] = new("Ukázková entita", "Entita pro demonstraci funkcionality"),
            
            // Systémové entity
            ["BusinessRule"] = new("Obchodní pravidlo", "Technická entita pro správu pravidel"),
            ["AuditTrail"] = new("Audit záznam", "Záznam o změnách v systému"),
            ["SystemLog"] = new("Systémový log", "Záznam systémových událostí")
        };
    }

    /// <summary>
    /// Získá display informace pro entitu
    /// </summary>
    private EntityDisplayInfo GetDisplayInfo(string entityName, Type entityType)
    {
        // Pokud máme konfigurované display informace, použijeme je
        if (_displayInfoMap.TryGetValue(entityName, out var configuredInfo))
        {
            return configuredInfo;
        }

        // Jinak vygenerujeme automaticky
        var displayName = GenerateDisplayName(entityName);
        var description = GenerateDescription(entityType);

        return new EntityDisplayInfo(displayName, description);
    }

    /// <summary>
    /// Generuje display name z názvu entity
    /// </summary>
    private string GenerateDisplayName(string entityName)
    {
        // Odstraníme "Entity" suffix pokud existuje
        var name = entityName.EndsWith("Entity") ? entityName[..^6] : entityName;
        
        // Převedeme PascalCase na čitelný text
        return System.Text.RegularExpressions.Regex.Replace(name, "([A-Z])", " $1").Trim();
    }

    /// <summary>
    /// Generuje popis entity z atributů nebo názvu
    /// </summary>
    private string GenerateDescription(Type entityType)
    {
        // Pokusíme se najít Description atribut
        var descriptionAttr = entityType.GetCustomAttribute<DescriptionAttribute>();
        if (descriptionAttr != null)
        {
            return descriptionAttr.Description;
        }

        // Pokusíme se najít Display atribut
        var displayAttr = entityType.GetCustomAttribute<DisplayAttribute>();
        if (displayAttr?.Description != null)
        {
            return displayAttr.Description;
        }

        // Fallback na generický popis
        return $"Entita typu {entityType.Name}";
    }

    /// <summary>
    /// Generuje metadata vlastností pro daný typ entity
    /// </summary>
    private List<PropertyMetadata> GeneratePropertyMetadata(Type entityType)
    {
        var properties = new List<PropertyMetadata>();

        // Získáme všechny veřejné vlastnosti
        var propertyInfos = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => p.CanRead && !IsIgnoredProperty(p))
            .OrderBy(p => GetPropertyOrder(p))
            .ThenBy(p => p.Name);

        foreach (var prop in propertyInfos)
        {
            var metadata = CreatePropertyMetadata(prop);
            if (metadata != null)
            {
                properties.Add(metadata);
            }
        }

        return properties;
    }

    /// <summary>
    /// Vytvoří metadata pro konkrétní vlastnost
    /// </summary>
    private PropertyMetadata? CreatePropertyMetadata(PropertyInfo property)
    {
        try
        {
            var displayName = GetPropertyDisplayName(property);
            var description = GetPropertyDescription(property);
            var typeName = GetPropertyTypeName(property.PropertyType);

            return new PropertyMetadata
            {
                Name = property.Name,
                DisplayName = displayName,
                Type = typeName,
                Description = description
            };
        }
        catch
        {
            // Pokud se nepodaří vytvořit metadata, přeskočíme vlastnost
            return null;
        }
    }

    /// <summary>
    /// Získá display name vlastnosti
    /// </summary>
    private string GetPropertyDisplayName(PropertyInfo property)
    {
        // Pokusíme se najít Display atribut
        var displayAttr = property.GetCustomAttribute<DisplayAttribute>();
        if (!string.IsNullOrEmpty(displayAttr?.Name))
        {
            return displayAttr.Name;
        }

        // Pokusíme se najít DisplayName atribut
        var displayNameAttr = property.GetCustomAttribute<DisplayNameAttribute>();
        if (!string.IsNullOrEmpty(displayNameAttr?.DisplayName))
        {
            return displayNameAttr.DisplayName;
        }

        // Fallback na převod PascalCase na čitelný text
        return System.Text.RegularExpressions.Regex.Replace(property.Name, "([A-Z])", " $1").Trim();
    }

    /// <summary>
    /// Získá popis vlastnosti
    /// </summary>
    private string GetPropertyDescription(PropertyInfo property)
    {
        // Pokusíme se najít Description atribut
        var descriptionAttr = property.GetCustomAttribute<DescriptionAttribute>();
        if (!string.IsNullOrEmpty(descriptionAttr?.Description))
        {
            return descriptionAttr.Description;
        }

        // Pokusíme se najít Display atribut s popisem
        var displayAttr = property.GetCustomAttribute<DisplayAttribute>();
        if (!string.IsNullOrEmpty(displayAttr?.Description))
        {
            return displayAttr.Description;
        }

        // Fallback na generický popis
        return $"Vlastnost {property.Name} typu {GetPropertyTypeName(property.PropertyType)}";
    }

    /// <summary>
    /// Získá název typu vlastnosti pro RuleEngine
    /// </summary>
    private string GetPropertyTypeName(Type propertyType)
    {
        // Ošetříme nullable typy
        var underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

        return underlyingType.Name switch
        {
            nameof(String) => "String",
            nameof(Int32) => "Integer",
            nameof(Int64) => "Integer",
            nameof(Decimal) => "Decimal",
            nameof(Double) => "Decimal",
            nameof(Single) => "Decimal",
            nameof(Boolean) => "Boolean",
            nameof(DateTime) => "DateTime",
            nameof(DateTimeOffset) => "DateTime",
            nameof(Guid) => "Guid",
            _ when underlyingType.IsEnum => underlyingType.Name,
            _ => "Object"
        };
    }

    /// <summary>
    /// Určuje, zda má být vlastnost ignorována
    /// </summary>
    private bool IsIgnoredProperty(PropertyInfo property)
    {
        // Ignorujeme navigační vlastnosti (virtual)
        if (property.GetMethod?.IsVirtual == true && !property.GetMethod.IsFinal)
        {
            return true;
        }

        // Ignorujeme kolekce (kromě byte[])
        if (property.PropertyType != typeof(byte[]) && 
            typeof(System.Collections.IEnumerable).IsAssignableFrom(property.PropertyType) &&
            property.PropertyType != typeof(string))
        {
            return true;
        }

        // Ignorujeme doménové události
        if (property.Name == nameof(BaseEntity<object>.DomainEvents))
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// Získá pořadí vlastnosti pro řazení
    /// </summary>
    private int GetPropertyOrder(PropertyInfo property)
    {
        // ID vlastnosti na začátek
        if (property.Name == "Id")
            return 0;

        // Základní vlastnosti
        if (property.Name.EndsWith("Name") || property.Name.EndsWith("Number"))
            return 1;

        // Datum vlastnosti
        if (property.PropertyType == typeof(DateTime) || property.PropertyType == typeof(DateTime?) ||
            property.PropertyType == typeof(DateTimeOffset) || property.PropertyType == typeof(DateTimeOffset?))
            return 2;

        // Číselné vlastnosti
        if (IsNumericType(property.PropertyType))
            return 3;

        // Boolean vlastnosti
        if (property.PropertyType == typeof(bool) || property.PropertyType == typeof(bool?))
            return 4;

        // Ostatní
        return 5;
    }

    /// <summary>
    /// Ověří, zda je typ číselný
    /// </summary>
    private bool IsNumericType(Type type)
    {
        var underlyingType = Nullable.GetUnderlyingType(type) ?? type;
        return underlyingType == typeof(int) || underlyingType == typeof(long) ||
               underlyingType == typeof(decimal) || underlyingType == typeof(double) ||
               underlyingType == typeof(float);
    }
}

/// <summary>
/// Pomocná třída pro display informace entity
/// </summary>
public record EntityDisplayInfo(string DisplayName, string? Description = null);
