using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Infrastructure.ExpressionEngine.API;

/// <summary>
/// Rozhraní pro repository výrazů.
/// </summary>
public interface IExpressionRepository
{
    /// <summary>
    /// Získá všechny výrazy.
    /// </summary>
    Task<IEnumerable<Expression>> GetAllAsync();

    /// <summary>
    /// Získá výraz podle ID.
    /// </summary>
    Task<Expression?> GetByIdAsync(Guid id);

    /// <summary>
    /// Získá výraz podle názvu.
    /// </summary>
    Task<Expression?> GetByNameAsync(string name);

    /// <summary>
    /// Přidá nový výraz.
    /// </summary>
    Task AddAsync(Expression expression);

    /// <summary>
    /// Aktualizuje existující výraz.
    /// </summary>
    Task UpdateAsync(Expression expression);

    /// <summary>
    /// Smaže výraz podle ID.
    /// </summary>
    Task DeleteAsync(Guid id);

    /// <summary>
    /// Získá aktivní výrazy pro konkrétní entitu.
    /// </summary>
    Task<IEnumerable<Expression>> GetActiveExpressionsForEntityAsync(string entityName);

    /// <summary>
    /// Ověří, zda existuje výraz s daným názvem.
    /// </summary>
    Task<bool> ExistsWithNameAsync(string name, Guid? excludeId = null);
}
