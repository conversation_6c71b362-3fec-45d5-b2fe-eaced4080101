using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Application.Abstraction;
using Domain.Entities;
using Infrastructure.ExpressionEngine.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.ExpressionEngine.API;

/// <summary>
/// Minimal API endpointy pro ExpressionEngine systém.
/// Poskytuje technické rozhraní pro vytváření, úpravu a testování výrazů.
/// </summary>
public static class ExpressionEngineEndpoints
{
    /// <summary>
    /// Registruje všechny ExpressionEngine endpointy.
    /// </summary>
    public static IEndpointRouteBuilder MapExpressionEngineEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/expression-engine")
            .WithTags("ExpressionEngine");

        // GET /api/expression-engine/expressions - Získá všechny výrazy
        group.MapGet("/expressions", async (
            [FromServices] IExpressionRepository repository) =>
        {
            try
            {
                var expressions = await repository.GetAllAsync();
                return Results.Ok(expressions);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání výrazů: {ex.Message}");
            }
        })
        .WithName("GetAllExpressions")
        .WithSummary("Získá všechny výrazy")
        .WithDescription("Vrací seznam všech výrazů v systému")
        .Produces<IEnumerable<Expression>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/expression-engine/expressions/{id} - Získá konkrétní výraz
        group.MapGet("/expressions/{id:guid}", async (
            Guid id,
            [FromServices] IExpressionRepository repository) =>
        {
            try
            {
                var expression = await repository.GetByIdAsync(id);
                if (expression == null)
                {
                    return Results.NotFound($"Výraz s ID {id} nebyl nalezen.");
                }
                return Results.Ok(expression);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání výrazu: {ex.Message}");
            }
        })
        .WithName("GetExpression")
        .WithSummary("Získá výraz podle ID")
        .WithDescription("Vrací konkrétní výraz včetně jeho struktury")
        .Produces<Expression>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/expression-engine/expressions - Vytvoří nové pravidlo
        group.MapPost("/expressions", async (
            [FromBody] Expression expression,
            [FromServices] IExpressionRepository repository) =>
        {
            try
            {
                // Kontrola duplicitního názvu
                if (await repository.ExistsWithNameAsync(expression.Name, null))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{expression.Name}' již existuje.");
                }

                // Nastavení základních vlastností
                expression.Id = Guid.NewGuid();
                expression.SchemaVersion = "1.0";

                // TODO: Přidat validaci syntaxe po opravě CalculationEngine
                // var validationResult = await engine.ValidateExpressionAsync(expression);
                // if (!validationResult.IsValid)
                // {
                //     return Results.BadRequest(new {
                //         Message = "Výraz obsahuje syntaktické chyby.",
                //         Errors = new[] { validationResult.ErrorMessage }
                //     });
                // }

                await repository.AddAsync(expression);
                return Results.CreatedAtRoute("GetExpression", new { id = expression.Id }, expression);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při vytváření pravidla: {ex.Message}");
            }
        })
        .WithName("CreateRule")
        .WithSummary("Vytvoří nové obchodní pravidlo")
        .WithDescription("Vytvoří nové obchodní pravidlo s validací syntaxe")
        .Accepts<Expression>("application/json")
        .Produces<Expression>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        // PUT /api/expression-engine/expressions/{id} - Aktualizuje pravidlo
        group.MapPut("/expressions/{id:guid}", async (
            Guid id,
            [FromBody] Expression updatedRule,
            [FromServices] IExpressionRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var existingRule = await repository.GetByIdAsync(id);
                if (existingRule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                // Kontrola duplicitního názvu (kromě aktuálního pravidla)
                if (await repository.ExistsWithNameAsync(updatedRule.Name, id))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{updatedRule.Name}' již existuje.");
                }

                // Aktualizace pravidla (zachování ID a RowVersion)
                existingRule.Name = updatedRule.Name;
                existingRule.Description = updatedRule.Description;
                existingRule.TargetEntityName = updatedRule.TargetEntityName;
                existingRule.TargetProperty = updatedRule.TargetProperty;
                existingRule.RootNode = updatedRule.RootNode;
                existingRule.IsActive = updatedRule.IsActive;
                existingRule.InternalNotes = updatedRule.InternalNotes;

                // TODO: Přidat validaci syntaxe po opravě CalculationEngine
                // var validationResult = await engine.ValidateExpressionAsync(existingExpression);
                // if (!validationResult.IsValid)
                // {
                //     return Results.BadRequest(new {
                //         Message = "Výraz obsahuje syntaktické chyby.",
                //         Errors = new[] { validationResult.ErrorMessage }
                //     });
                // }

                await repository.UpdateAsync(existingRule);

                // Vymazání z cache
                // await engine.InvalidateExpressionAsync(id);

                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při aktualizaci pravidla: {ex.Message}");
            }
        })
        .WithName("UpdateRule")
        .WithSummary("Aktualizuje obchodní pravidlo")
        .WithDescription("Aktualizuje existující obchodní pravidlo s validací syntaxe")
        .Accepts<Expression>("application/json")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // DELETE /api/expression-engine/expressions/{id} - Smaže pravidlo
        group.MapDelete("/expressions/{id:guid}", async (
            Guid id,
            [FromServices] IExpressionRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var rule = await repository.GetByIdAsync(id);
                if (rule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                await repository.DeleteAsync(id);
                
                // Vymazání z cache
                // await engine.InvalidateExpressionAsync(id);
                
                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při mazání pravidla: {ex.Message}");
            }
        })
        .WithName("DeleteExpression")
        .WithSummary("Smaže výraz")
        .WithDescription("Smaže existující výraz ze systému")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/expression-engine/expressions/{id}/test-with-entity - Testuje výraz s reálnou entitou
        group.MapPost("/expressions/{id:guid}/test-with-entity", async (
            Guid id,
            [FromBody] ExpressionTestRequest request,
            [FromServices] IExpressionRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var expression = await repository.GetByIdAsync(id);
                if (expression == null)
                {
                    return Results.NotFound($"Výraz s ID {id} nebyl nalezen.");
                }

                // Deserializace entity dat podle typu
                object entity;
                try
                {
                    entity = request.EntityType.ToLowerInvariant() switch
                    {
                        "sampleentity" => JsonSerializer.Deserialize<SampleEntity>(request.EntityData, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
                        _ => throw new ArgumentException($"Nepodporovaný typ entity: {request.EntityType}")
                    };

                    if (entity == null)
                    {
                        return Results.BadRequest("Nepodařilo se deserializovat data entity.");
                    }
                }
                catch (JsonException ex)
                {
                    return Results.BadRequest($"Chyba při deserializaci entity dat: {ex.Message}");
                }

                // Spuštění výrazu
                var startTime = DateTime.UtcNow;
                var result = await engine.ExecuteAsync(expression, entity);
                var executionTime = DateTime.UtcNow - startTime;

                return Results.Ok(new ExpressionTestResponse
                {
                    ExpressionId = expression.Id,
                    ExpressionName = expression.Name,
                    EntityType = request.EntityType,
                    Result = result,
                    ExecutionTimeMs = executionTime.TotalMilliseconds,
                    Success = true,
                    ErrorMessage = null
                });
            }
            catch (Exception ex)
            {
                return Results.Ok(new ExpressionTestResponse
                {
                    ExpressionId = id,
                    ExpressionName = null,
                    EntityType = request?.EntityType,
                    Result = null,
                    ExecutionTimeMs = 0,
                    Success = false,
                    ErrorMessage = ex.Message
                });
            }
        })
        .WithName("TestExpressionWithEntity")
        .WithSummary("Testuje výraz s reálnou entitou")
        .WithDescription("Spustí výraz nad poskytnutou entitou a vrátí výsledek s diagnostickými informacemi")
        .Accepts<ExpressionTestRequest>("application/json")
        .Produces<ExpressionTestResponse>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound);

        // GET /api/expression-engine/sample-entities - Získá ukázkové entity pro testování
        group.MapGet("/sample-entities", async (
            [FromServices] IApplicationDbContext context,
            [FromQuery] int take = 5) =>
        {
            try
            {
                var sampleEntities = await context.Set<SampleEntity>()
                    .Take(take)
                    .ToListAsync();

                return Results.Ok(new
                {
                    EntityType = "SampleEntity",
                    Count = sampleEntities.Count,
                    Entities = sampleEntities
                });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání ukázkových entit: {ex.Message}");
            }
        })
        .WithName("GetSampleEntities")
        .WithSummary("Získá ukázkové entity pro testování")
        .WithDescription("Vrací seznam SampleEntity z databáze pro testování výrazů")
        .Produces(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }
}

/// <summary>
/// Request objekt pro testování výrazu s entitou.
/// </summary>
public class ExpressionTestRequest
{
    /// <summary>
    /// Typ entity (např. "SampleEntity").
    /// </summary>
    public required string EntityType { get; set; }

    /// <summary>
    /// JSON data entity.
    /// </summary>
    public required JsonElement EntityData { get; set; }
}

/// <summary>
/// Response objekt s výsledkem testování výrazu.
/// </summary>
public class ExpressionTestResponse
{
    /// <summary>
    /// ID testovaného výrazu.
    /// </summary>
    public Guid ExpressionId { get; set; }

    /// <summary>
    /// Název testovaného výrazu.
    /// </summary>
    public string? ExpressionName { get; set; }

    /// <summary>
    /// Typ entity použité pro testování.
    /// </summary>
    public string? EntityType { get; set; }

    /// <summary>
    /// Výsledek vykonání výrazu.
    /// </summary>
    public object? Result { get; set; }

    /// <summary>
    /// Čas vykonání v milisekundách.
    /// </summary>
    public double ExecutionTimeMs { get; set; }

    /// <summary>
    /// Indikuje, zda bylo testování úspěšné.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Chybová zpráva v případě neúspěchu.
    /// </summary>
    public string? ErrorMessage { get; set; }
}
