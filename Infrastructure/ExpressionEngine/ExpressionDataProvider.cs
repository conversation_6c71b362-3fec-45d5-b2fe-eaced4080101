using System.Linq.Expressions;
using Application.Abstraction;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.ExpressionEngine;

/// <summary>
/// Implementace IExpressionDataProvider pro přístup k datům při vyhodnocování výrazů.
/// Používá ApplicationDbContext pro dotazování na entity.
/// </summary>
public class ExpressionDataProvider : IExpressionDataProvider
{
    private readonly IApplicationDbContext _context;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;

    /// <summary>
    /// Inicializuje novou instanci ExpressionDataProvider.
    /// </summary>
    /// <param name="context">Databázový kontext pro přístup k datům</param>
    /// <param name="entityTypeMap">Mapa názvů entit na jejich typy</param>
    public ExpressionDataProvider(
        IApplicationDbContext context,
        IReadOnlyDictionary<string, Type> entityTypeMap)
    {
        _context = context;
        _entityTypeMap = entityTypeMap;
    }

    /// <summary>
    /// Najde jediný záznam podle Expression filtru nebo vyhodí NotFoundException.
    /// </summary>
    /// <param name="entityName">Název entity pro vyhledání</param>
    /// <param name="filterExpression">Lambda výraz pro filtrování</param>
    /// <returns>Nalezená entita</returns>
    /// <exception cref="ArgumentException">Pokud entita není registrována</exception>
    /// <exception cref="InvalidOperationException">Pokud není nalezen právě jeden záznam</exception>
    public object FindSingle(string entityName, Expression filterExpression)
    {
        if (!_entityTypeMap.TryGetValue(entityName, out var entityType))
        {
            throw new ArgumentException($"Entity '{entityName}' není registrována v typu mapě.", nameof(entityName));
        }

        try
        {
            // Získáme DbSet pro daný typ entity
            var dbSetProperty = _context.GetType().GetProperties()
                .FirstOrDefault(p => p.PropertyType.IsGenericType &&
                                   p.PropertyType.GetGenericTypeDefinition() == typeof(DbSet<>) &&
                                   p.PropertyType.GetGenericArguments()[0] == entityType);

            if (dbSetProperty == null)
            {
                throw new InvalidOperationException($"DbSet pro entitu '{entityName}' nebyl nalezen v kontextu.");
            }

            var dbSet = dbSetProperty.GetValue(_context);
            if (dbSet == null)
            {
                throw new InvalidOperationException($"DbSet pro entitu '{entityName}' je null.");
            }

            // Vytvoříme IQueryable z DbSet
            var queryableMethod = typeof(Queryable).GetMethod("AsQueryable", new[] { typeof(System.Collections.IEnumerable) });
            if (queryableMethod == null)
            {
                throw new InvalidOperationException("AsQueryable metoda nebyla nalezena.");
            }

            var queryable = queryableMethod.Invoke(null, new[] { dbSet });

            // Aplikujeme filtr pomocí Where metody
            var whereMethod = typeof(Queryable).GetMethods()
                .First(m => m.Name == "Where" && 
                           m.GetParameters().Length == 2 &&
                           m.GetParameters()[1].ParameterType.GetGenericTypeDefinition() == typeof(Expression<>))
                .MakeGenericMethod(entityType);

            var filteredQueryable = whereMethod.Invoke(null, new[] { queryable, filterExpression });

            // Zavoláme Single() metodu
            var singleMethod = typeof(Queryable).GetMethods()
                .First(m => m.Name == "Single" && m.GetParameters().Length == 1)
                .MakeGenericMethod(entityType);

            var result = singleMethod.Invoke(null, new[] { filteredQueryable });

            if (result == null)
            {
                throw new InvalidOperationException($"Žádný záznam nebyl nalezen pro entitu '{entityName}' s daným filtrem.");
            }

            return result;
        }
        catch (System.Reflection.TargetInvocationException ex) when (ex.InnerException != null)
        {
            // Rozbalíme vnitřní výjimku z reflection volání
            throw ex.InnerException;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Chyba při vyhledávání entity '{entityName}': {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Najde více záznamů podle Expression filtru.
    /// </summary>
    /// <param name="entityName">Název entity pro vyhledání</param>
    /// <param name="filterExpression">Lambda výraz pro filtrování</param>
    /// <returns>Kolekce nalezených entit</returns>
    /// <exception cref="ArgumentException">Pokud entita není registrována</exception>
    /// <exception cref="InvalidOperationException">Pokud dojde k chybě při dotazování</exception>
    public IEnumerable<object> FindMany(string entityName, Expression filterExpression)
    {
        if (!_entityTypeMap.TryGetValue(entityName, out var entityType))
        {
            throw new ArgumentException($"Entity '{entityName}' není registrována v typu mapě.", nameof(entityName));
        }

        try
        {
            // Získáme DbSet pro daný typ entity
            var dbSetProperty = _context.GetType().GetProperties()
                .FirstOrDefault(p => p.PropertyType.IsGenericType &&
                                   p.PropertyType.GetGenericTypeDefinition() == typeof(DbSet<>) &&
                                   p.PropertyType.GetGenericArguments()[0] == entityType);

            if (dbSetProperty == null)
            {
                throw new InvalidOperationException($"DbSet pro entitu '{entityName}' nebyl nalezen v kontextu.");
            }

            var dbSet = dbSetProperty.GetValue(_context);
            if (dbSet == null)
            {
                throw new InvalidOperationException($"DbSet pro entitu '{entityName}' je null.");
            }

            // Vytvoříme IQueryable z DbSet
            var queryableMethod = typeof(Queryable).GetMethod("AsQueryable", new[] { typeof(System.Collections.IEnumerable) });
            if (queryableMethod == null)
            {
                throw new InvalidOperationException("AsQueryable metoda nebyla nalezena.");
            }

            var queryable = queryableMethod.Invoke(null, new[] { dbSet });

            // Aplikujeme filtr pomocí Where metody
            var whereMethod = typeof(Queryable).GetMethods()
                .First(m => m.Name == "Where" &&
                           m.GetParameters().Length == 2 &&
                           m.GetParameters()[1].ParameterType.GetGenericTypeDefinition() == typeof(Expression<>))
                .MakeGenericMethod(entityType);

            var filteredQueryable = whereMethod.Invoke(null, new[] { queryable, filterExpression });

            // Zavoláme ToList() metodu pro získání všech záznamů
            var toListMethod = typeof(Enumerable).GetMethods()
                .First(m => m.Name == "ToList" && m.GetParameters().Length == 1)
                .MakeGenericMethod(entityType);

            var result = toListMethod.Invoke(null, new[] { filteredQueryable });

            if (result == null)
            {
                return Enumerable.Empty<object>();
            }

            // Převedeme na IEnumerable<object>
            return ((System.Collections.IEnumerable)result).Cast<object>();
        }
        catch (System.Reflection.TargetInvocationException ex) when (ex.InnerException != null)
        {
            // Rozbalíme vnitřní výjimku z reflection volání
            throw ex.InnerException;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Chyba při vyhledávání entit '{entityName}': {ex.Message}", ex);
        }
    }
}
