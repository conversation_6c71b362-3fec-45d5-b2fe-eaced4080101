using System.Linq.Expressions;

namespace Infrastructure.ExpressionEngine;

public interface IExpressionDataProvider
{
    /// <summary>
    /// Najde jediný záznam podle Expression filtru nebo vyhodí NotFoundException.
    /// </summary>
    object FindSingle(string entityName, Expression filterExpression);

    /// <summary>
    /// Najde více záznamů podle Expression filtru.
    /// </summary>
    IEnumerable<object> FindMany(string entityName, Expression filterExpression);
}