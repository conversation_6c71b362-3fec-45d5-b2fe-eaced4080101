namespace Infrastructure.ExpressionEngine;

/// <summary>
/// Specializovaná cache služba pro Expression Engine.
/// Poskytuje cachování zkompilovaných výrazů, databázových dotazů a výsledků validací.
/// </summary>
public interface IExpressionCacheService
{
    /// <summary>
    /// Získá hodnotu z cache nebo ji vytvoří pomocí factory funkce.
    /// </summary>
    /// <typeparam name="T">Typ cachované hodnoty</typeparam>
    /// <param name="key">Klíč pro cache</param>
    /// <param name="factory">Funkce pro vytvoření hodnoty, pokud není v cache</param>
    /// <param name="expiry">Doba expiraci (volitelné, výchozí 10 minut)</param>
    /// <returns>Cachovaná nebo nově vytvořená hodnota</returns>
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null);

    /// <summary>
    /// Získá hodnotu z cache nebo ji vytvoří pomocí synchronní factory funkce.
    /// </summary>
    /// <typeparam name="T">Typ cachované hodnoty</typeparam>
    /// <param name="key">Klíč pro cache</param>
    /// <param name="factory">Synchronní funkce pro vytvoření hodnoty</param>
    /// <param name="expiry">Doba expiraci (volitelné, výchozí 10 minut)</param>
    /// <returns>Cachovaná nebo nově vytvořená hodnota</returns>
    Task<T> GetOrSetAsync<T>(string key, Func<T> factory, TimeSpan? expiry = null);

    /// <summary>
    /// Invaliduje cache záznamy podle pattern.
    /// Podporuje wildcards: * pro libovolný počet znaků, ? pro jeden znak.
    /// </summary>
    /// <param name="pattern">Pattern pro vyhledání klíčů k invalidaci</param>
    /// <returns>Počet invalidovaných záznamů</returns>
    Task<int> InvalidateAsync(string pattern);

    /// <summary>
    /// Odstraní konkrétní klíč z cache.
    /// </summary>
    /// <param name="key">Klíč k odstranění</param>
    /// <returns>True pokud byl klíč odstraněn, false pokud neexistoval</returns>
    Task<bool> RemoveAsync(string key);

    /// <summary>
    /// Vymaže celou cache.
    /// </summary>
    Task ClearAsync();

    /// <summary>
    /// Získá statistiky cache (počet záznamů, hit rate, atd.).
    /// </summary>
    /// <returns>Statistiky cache</returns>
    ExpressionCacheStatistics GetStatistics();
}

/// <summary>
/// Statistiky cache pro monitoring a debugging.
/// </summary>
public class ExpressionCacheStatistics
{
    /// <summary>
    /// Celkový počet záznamů v cache.
    /// </summary>
    public int TotalEntries { get; set; }

    /// <summary>
    /// Počet cache hitů.
    /// </summary>
    public long HitCount { get; set; }

    /// <summary>
    /// Počet cache missů.
    /// </summary>
    public long MissCount { get; set; }

    /// <summary>
    /// Hit rate v procentech (0-100).
    /// </summary>
    public double HitRate => TotalRequests > 0 ? (double)HitCount / TotalRequests * 100 : 0;

    /// <summary>
    /// Celkový počet požadavků na cache.
    /// </summary>
    public long TotalRequests => HitCount + MissCount;
}
