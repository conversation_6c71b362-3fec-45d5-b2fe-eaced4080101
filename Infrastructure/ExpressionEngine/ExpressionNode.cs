namespace Infrastructure.ExpressionEngine;

/// <summary>
/// Abstraktní základní třída pro všechny uzly v stromu výrazu.
/// Implementuje Composite pattern pro hierarchickou strukturu výrazů.
/// </summary>
public abstract class ExpressionNode
{
    /// <summary>
    /// Diskriminátor pro JSON serializaci a identifikaci typu uzlu.
    /// </summary>
    public abstract string NodeType { get; }
}

/// <summary>
/// Uzel reprezentující operaci s jedním nebo více operandy.
/// Podporuje aritmetické, logické a podmíněné operace.
/// </summary>
public class OperationNode : ExpressionNode
{
    public override string NodeType => "Operation";

    /// <summary>
    /// Typ operace, která má být provedena.
    /// </summary>
    public required OperatorType Operator { get; set; }

    /// <summary>
    /// Seznam operandů pro danou operaci.
    /// </summary>
    public List<ExpressionNode> Operands { get; set; } = new();
}

/// <summary>
/// Uzel reprezentující přístup k hodnotě ze zdrojové entity.
/// Podporuje navigaci přes vlastnosti pomocí tečkové notace.
/// </summary>
public class SourceValueNode : ExpressionNode
{
    public override string NodeType => "SourceValue";

    /// <summary>
    /// Cesta k vlastnosti ve zdrojové entitě (např. "Customer.Address.City").
    /// </summary>
    public required string SourcePath { get; set; }
}

/// <summary>
/// Uzel reprezentující konstantní hodnotu ve výrazu.
/// </summary>
public class ConstantNode : ExpressionNode
{
    public override string NodeType => "Constant";

    /// <summary>
    /// Datový typ konstanty pro správnou konverzi.
    /// </summary>
    public required ValueType DataType { get; set; }

    /// <summary>
    /// Textová reprezentace hodnoty konstanty.
    /// </summary>
    public required string Value { get; set; }
}

/// <summary>
/// Uzel reprezentující agregační operaci nad kolekcí.
/// Podporuje Sum, Count, Average operace s možností filtrování.
/// </summary>
public class AggregationNode : ExpressionNode
{
    public override string NodeType => "Aggregation";

    /// <summary>
    /// Typ agregační operace.
    /// </summary>
    public required AggregationType AggregationType { get; set; }

    /// <summary>
    /// Cesta ke kolekci ve zdrojové entitě.
    /// </summary>
    public required string CollectionPath { get; set; }

    /// <summary>
    /// Cesta k poli v prvcích kolekce pro agregaci (volitelné pro Count).
    /// </summary>
    public string? FieldPath { get; set; }

    /// <summary>
    /// Volitelný filtr pro omezení prvků kolekce před agregací.
    /// </summary>
    public ExpressionNode? Filter { get; set; }
}

/// <summary>
/// Uzel reprezentující vyhledání entity podle podmínky.
/// Umožňuje cross-entity reference ve výrazech.
/// </summary>
public class LookupNode : ExpressionNode
{
    public override string NodeType => "Lookup";

    /// <summary>
    /// Název cílové entity pro vyhledání.
    /// </summary>
    public required string TargetEntityName { get; set; }

    /// <summary>
    /// Cesta k poli, které má být vráceno z nalezené entity.
    /// </summary>
    public required string ReturnFieldPath { get; set; }

    /// <summary>
    /// Podmínka pro vyhledání entity.
    /// </summary>
    public required ExpressionNode Condition { get; set; }
}

/// <summary>
/// Výčet podporovaných operátorů ve výrazech.
/// </summary>
public enum OperatorType 
{ 
    Add, 
    Subtract, 
    Multiply, 
    Divide, 
    Equal, 
    NotEqual, 
    GreaterThan, 
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    And, 
    Or, 
    Not,
    If 
}

/// <summary>
/// Výčet podporovaných agregačních operací.
/// </summary>
public enum AggregationType 
{ 
    Sum, 
    Count, 
    Average,
    Min,
    Max
}

/// <summary>
/// Výčet podporovaných datových typů pro konstanty.
/// </summary>
public enum ValueType
{
    String,
    Decimal,
    Integer,
    Boolean,
    DateTime,
    Guid
}

/// <summary>
/// Uzel pro komplexní agregace přes vztahy mezi entitami.
/// Umožňuje najít související entity a provést nad nimi agregaci.
/// </summary>
public class RelatedAggregationNode : ExpressionNode
{
    public override string NodeType => "RelatedAggregation";

    /// <summary>
    /// Název zdrojové entity (např. "Order")
    /// </summary>
    public required string SourceEntityName { get; set; }

    /// <summary>
    /// Název cílové entity pro agregaci (např. "Invoice")
    /// </summary>
    public required string TargetEntityName { get; set; }

    /// <summary>
    /// Vlastnost pro propojení entit (např. "CustomerId")
    /// </summary>
    public required string RelationshipProperty { get; set; }

    /// <summary>
    /// Typ agregace (Sum, Count, Average, Min, Max)
    /// </summary>
    public required AggregationType AggregationType { get; set; }

    /// <summary>
    /// Název pole pro agregaci (např. "TotalAmount")
    /// </summary>
    public string? AggregationField { get; set; }

    /// <summary>
    /// Podmínka pro filtrování cílových entit
    /// </summary>
    public ExpressionNode? FilterCondition { get; set; }
}
