using System.ComponentModel.DataAnnotations;

namespace Infrastructure.ExpressionEngine;

/// <summary>
/// Technická entita reprezentující výraz pro výpočty a validace.
/// Obsahuje definici výrazu ve formě stromu uzlů (ExpressionNode).
/// Jedná se o ryze technickou entitu bez trackování změn.
/// </summary>
public class Expression
{
    /// <summary>
    /// Jedinečný identifikátor výrazu.
    /// </summary>
    [Key]
    public Guid Id { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// </summary>
    [Timestamp]
    public byte[] RowVersion { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Název výrazu pro identifikaci uživatelem.
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// Popis výrazu vysvětluj<PERSON><PERSON><PERSON> jeho ú<PERSON>el a použití.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Název cílové entity, na kterou se výraz aplikuje.
    /// </summary>
    public required string TargetEntityName { get; set; }

    /// <summary>
    /// Název vlastnosti cílové entity, která bude výsledkem výrazu.
    /// </summary>
    public string? TargetProperty { get; set; }

    /// <summary>
    /// Verze schématu výrazu pro zajištění kompatibility při změnách.
    /// </summary>
    public string SchemaVersion { get; set; } = "1.0";

    /// <summary>
    /// Kořenový uzel stromu výrazu obsahující logiku výpočtu.
    /// </summary>
    public required ExpressionNode RootNode { get; set; }

    /// <summary>
    /// Určuje, zda je výraz aktivní a měl by být vykonáván.
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Interní poznámky pro vývojáře.
    /// </summary>
    public string? InternalNotes { get; set; }

    /// <summary>
    /// Číslo verze výrazu pro podporu verzování.
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Datum a čas, od kterého je tato verze výrazu platná.
    /// </summary>
    public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Datum a čas, do kterého je tato verze výrazu platná.
    /// Null znamená, že verze je stále aktivní.
    /// </summary>
    public DateTime? EffectiveTo { get; set; }
}
