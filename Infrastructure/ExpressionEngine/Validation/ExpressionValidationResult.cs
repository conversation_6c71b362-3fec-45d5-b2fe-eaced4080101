namespace Infrastructure.ExpressionEngine.Validation;

/// <summary>
/// Výsledek validace výrazu.
/// </summary>
public class ExpressionValidationResult
{
    /// <summary>
    /// Určuje, zda je výraz syntakticky správný.
    /// </summary>
    public bool IsValid { get; private set; }

    /// <summary>
    /// Chybová zpráva v případě neplatného výrazu.
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Seznam všech nalezených chyb.
    /// </summary>
    public List<string> Errors { get; private set; } = new();

    private ExpressionValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
        if (!string.IsNullOrEmpty(errorMessage))
        {
            Errors.Add(errorMessage);
        }
    }

    /// <summary>
    /// Vyt<PERSON><PERSON><PERSON> výsledek pro platný výraz.
    /// </summary>
    /// <returns>Platný výsledek validace</returns>
    public static ExpressionValidationResult Valid()
    {
        return new ExpressionValidationResult(true);
    }

    /// <summary>
    /// Vytvoří výsledek pro neplatný výraz s chybovou zprávou.
    /// </summary>
    /// <param name="errorMessage">Popis chyby</param>
    /// <returns>Neplatný výsledek validace</returns>
    public static ExpressionValidationResult Invalid(string errorMessage)
    {
        return new ExpressionValidationResult(false, errorMessage);
    }

    /// <summary>
    /// Vytvoří výsledek pro neplatný výraz s více chybami.
    /// </summary>
    /// <param name="errors">Seznam chyb</param>
    /// <returns>Neplatný výsledek validace</returns>
    public static ExpressionValidationResult Invalid(IEnumerable<string> errors)
    {
        var result = new ExpressionValidationResult(false);
        result.Errors.AddRange(errors);
        result.ErrorMessage = string.Join("; ", errors);
        return result;
    }

    /// <summary>
    /// Přidá další chybu k existujícímu výsledku.
    /// </summary>
    /// <param name="error">Chybová zpráva</param>
    public void AddError(string error)
    {
        IsValid = false;
        Errors.Add(error);
        ErrorMessage = string.Join("; ", Errors);
    }

    /// <summary>
    /// Kombinuje dva výsledky validace.
    /// </summary>
    /// <param name="other">Další výsledek validace</param>
    /// <returns>Kombinovaný výsledek</returns>
    public ExpressionValidationResult Combine(ExpressionValidationResult other)
    {
        if (IsValid && other.IsValid)
            return Valid();

        var allErrors = Errors.Concat(other.Errors).ToList();
        return Invalid(allErrors);
    }

    public override string ToString()
    {
        return IsValid ? "Platný výraz" : $"Neplatný výraz: {ErrorMessage}";
    }
}
