# RuleEngine API Dokumentace

## Přehled

RuleEngine API poskytuje technické rozhraní pro vytváření, správu a testování obchodních pravidel za běhu aplikace. API je navrženo pro jednoduchost a spolehlivost při práci s desítkami pravidel.

## Base URL

```
/api/rule-engine
```

## Autentifikace

API používá standardní autentifikaci aplikace. Všechny endpointy vyžadují platnou autentifikaci.

## Endpointy

### Správa pravidel

#### GET /rules
Získá seznam všech obchodních pravidel.

**Response:**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Výpočet slevy",
    "description": "Automatický výpočet slevy podle objemu",
    "targetEntityName": "Order",
    "targetProperty": "Discount",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00Z",
    "modifiedAt": "2024-01-15T14:20:00Z"
  }
]
```

#### GET /rules/{id}
Získá konkrétní obchodní pravidlo podle ID.

**Parameters:**
- `id` (path, required): GUID identifikátor pravidla

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "rowVersion": "AAAAAAAAAAE=",
  "name": "Výpočet slevy",
  "description": "Automatický výpočet slevy podle objemu",
  "targetEntityName": "Order",
  "targetProperty": "Discount",
  "schemaVersion": "1.0",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "Multiply",
    "operands": [...]
  },
  "isActive": true,
  "internalNotes": null
}
```

#### POST /rules
Vytvoří nové obchodní pravidlo.

**Request Body:**
```json
{
  "name": "Nové pravidlo",
  "description": "Popis pravidla",
  "targetEntityName": "Order",
  "targetProperty": "Discount",
  "rootNode": {
    "nodeType": "Constant",
    "dataType": "Decimal",
    "value": "0.1"
  },
  "isActive": true,
  "internalNotes": "Poznámky pro vývojáře"
}
```

**Response:** 201 Created s vytvořeným pravidlem

#### PUT /rules/{id}
Aktualizuje existující obchodní pravidlo.

**Parameters:**
- `id` (path, required): GUID identifikátor pravidla

**Request Body:** Stejný jako POST /rules

**Response:** 204 No Content

#### DELETE /rules/{id}
Smaže obchodní pravidlo.

**Parameters:**
- `id` (path, required): GUID identifikátor pravidla

**Response:** 204 No Content

### Utility operace

#### POST /rules/validate
Validuje syntaktickou správnost pravidla bez jeho uložení.

**Request Body:**
```json
{
  "name": "Test pravidlo",
  "targetEntityName": "Order",
  "rootNode": {
    "nodeType": "ConstantNode",
    "dataType": "Integer",
    "value": "42"
  }
}
```

**Response:**
```json
{
  "isValid": true,
  "errors": [],
  "message": "Pravidlo je syntakticky správné."
}
```

#### POST /rules/{id}/test
Testuje vykonání pravidla na ukázkových datech.

**Parameters:**
- `id` (path, required): GUID identifikátor pravidla

**Request Body:** Libovolný JSON objekt s testovacími daty

**Response:**
```json
{
  "success": true,
  "result": 42,
  "message": "Pravidlo bylo úspěšně vykonáno."
}
```

#### POST /rules/{id}/duplicate
Duplikuje existující pravidlo s novým názvem.

**Parameters:**
- `id` (path, required): GUID identifikátor pravidla

**Request Body:**
```json
{
  "newName": "Kopie původního pravidla",
  "newDescription": "Popis kopie"
}
```

**Response:** 201 Created s duplikovaným pravidlem

#### PATCH /rules/{id}/status
Aktivuje nebo deaktivuje pravidlo.

**Parameters:**
- `id` (path, required): GUID identifikátor pravidla

**Request Body:**
```json
{
  "isActive": true
}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "isActive": true,
  "message": "Pravidlo bylo aktivováno."
}
```

### Metadata API

#### GET /metadata
Získá metadata potřebná pro vytváření pravidel.

**Response:**
```json
{
  "availableEntities": [
    {
      "name": "Order",
      "displayName": "Objednávka",
      "description": "Objednávka zákazníka"
    }
  ],
  "availableOperators": [
    {
      "value": "Add",
      "displayName": "Sčítání (+)",
      "category": "Aritmetické",
      "description": "Sečte dva číselné operandy"
    }
  ],
  "availableAggregations": [
    {
      "value": "Sum",
      "displayName": "Součet",
      "description": "Sečte hodnoty zadaného pole",
      "requiresField": true
    }
  ],
  "availableValueTypes": [
    {
      "value": "Integer",
      "displayName": "Celé číslo",
      "description": "Celé číslo (např. 42, -10)"
    }
  ],
  "schemaVersion": "1.0"
}
```

#### GET /entities/{entityName}/properties
Získá vlastnosti konkrétní entity.

**Parameters:**
- `entityName` (path, required): Název entity

**Response:**
```json
[
  {
    "name": "Id",
    "displayName": "ID",
    "type": "Guid",
    "description": "Jedinečný identifikátor"
  },
  {
    "name": "Amount",
    "displayName": "Částka",
    "type": "Decimal",
    "description": "Celková částka objednávky"
  }
]
```

### Export/Import

#### GET /export
Exportuje pravidla do JSON formátu.

**Query Parameters:**
- `activeOnly` (optional): Exportovat pouze aktivní pravidla (default: false)

**Response:**
```json
{
  "exportedAt": "2024-01-15T10:30:00Z",
  "schemaVersion": "1.0",
  "rules": [
    {
      "name": "Výpočet slevy",
      "description": "Automatický výpočet slevy",
      "targetEntityName": "Order",
      "targetProperty": "Discount",
      "rootNode": {...},
      "isActive": true
    }
  ]
}
```

#### POST /import
Importuje pravidla z JSON formátu.

**Request Body:**
```json
{
  "rules": [
    {
      "name": "Importované pravidlo",
      "description": "Popis",
      "targetEntityName": "Order",
      "rootNode": {...},
      "isActive": true
    }
  ],
  "overwriteExisting": false,
  "importAsActive": false
}
```

**Response:**
```json
{
  "summary": {
    "total": 1,
    "successful": 1,
    "failed": 0
  },
  "results": [
    {
      "ruleName": "Importované pravidlo",
      "success": true,
      "message": "Pravidlo bylo úspěšně importováno."
    }
  ]
}
```

## Error Handling

API používá standardní HTTP status kódy:

- `200 OK` - Úspěšná operace
- `201 Created` - Úspěšně vytvořeno
- `204 No Content` - Úspěšná operace bez obsahu
- `400 Bad Request` - Neplatný požadavek
- `404 Not Found` - Zdroj nenalezen
- `500 Internal Server Error` - Serverová chyba

Chybové odpovědi obsahují detailní informace:

```json
{
  "message": "Pravidlo obsahuje syntaktické chyby.",
  "errors": [
    "Neznámá cílová entita: InvalidEntity"
  ]
}
```

## Příklady použití

### Vytvoření jednoduchého pravidla

```javascript
// Pravidlo: Sleva 10% pro objednávky nad 1000 Kč
const rule = {
  name: "Sleva nad 1000",
  description: "10% sleva pro velké objednávky",
  targetEntityName: "Order",
  targetProperty: "Discount",
  rootNode: {
    nodeType: "OperationNode",
    operator: "If",
    operands: [
      {
        nodeType: "OperationNode",
        operator: "GreaterThan",
        operands: [
          { nodeType: "SourceValueNode", sourcePath: "Amount" },
          { nodeType: "ConstantNode", dataType: "Decimal", value: "1000" }
        ]
      },
      {
        nodeType: "OperationNode",
        operator: "Multiply",
        operands: [
          { nodeType: "SourceValueNode", sourcePath: "Amount" },
          { nodeType: "ConstantNode", dataType: "Decimal", value: "0.1" }
        ]
      },
      { nodeType: "ConstantNode", dataType: "Decimal", value: "0" }
    ]
  },
  isActive: true
};

const response = await fetch('/api/rule-engine/rules', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(rule)
});
```

### Testování pravidla

```javascript
const testData = {
  id: "123e4567-e89b-12d3-a456-************",
  amount: 1500,
  customerId: "customer-123"
};

const response = await fetch(`/api/rule-engine/rules/${ruleId}/test`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(testData)
});

const result = await response.json();
// result.result = 150 (10% z 1500)
```
