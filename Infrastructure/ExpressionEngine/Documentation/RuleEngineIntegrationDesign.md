# Rule Engine - Integrace s generickou správou entit

## Přehled problému

Potřebujeme definovat styčné body mezi Rule Engine a generickou správou entit. Hlavní otázky:

1. **Automatické spouštění** - <PERSON><PERSON> se pravidla spouštět automaticky při CRUD operacích?
2. **Ruční spouštění** - Nebo pouze na vyžádání uživatele/aplikace?
3. **Hybridní přístup** - Kombinace obou přístupů podle typu pravidla?
4. **Integrace s interceptory** - Jak využít existující infrastrukturu?

## Navrhované řešení - Hybridní přístup

### 1. Kategorizace pravidel podle způsobu spouštění

```csharp
public enum RuleExecutionMode
{
    Manual = 0,           // Pouze ruční spouštění
    OnCreate = 1,         // Automaticky při vytváření entity
    OnUpdate = 2,         // Automaticky při aktualizaci entity
    OnCreateOrUpdate = 3, // Automaticky při vytváření nebo aktualizaci
    OnDelete = 4,         // Automaticky při mazání entity
    OnAnyChange = 7       // Automaticky při jakékoliv změně (Create | Update | Delete)
}

public enum RuleResultAction
{
    SetProperty = 0,      // Nastaví hodnotu vlastnosti entity
    Validate = 1,         // Validuje entitu (true/false)
    Calculate = 2,        // Vypočítá hodnotu (nemusí se ukládat)
    Trigger = 3          // Spustí akci (např. notifikaci)
}
```

### 2. Rozšíření BusinessRule entity

```csharp
public class BusinessRule
{
    // Existující vlastnosti...
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string? Description { get; set; }
    public string TargetEntityName { get; set; }
    public string? TargetProperty { get; set; }
    public RuleNode RootNode { get; set; }
    public bool IsActive { get; set; }
    
    // Nové vlastnosti pro integraci
    public RuleExecutionMode ExecutionMode { get; set; } = RuleExecutionMode.Manual;
    public RuleResultAction ResultAction { get; set; } = RuleResultAction.SetProperty;
    public int Priority { get; set; } = 0; // Pro řazení pravidel
    public string? Condition { get; set; } // Dodatečná podmínka pro spuštění
}
```

### 3. Nový RuleExecutionInterceptor

```csharp
public class RuleExecutionInterceptor : SaveChangesInterceptor
{
    private readonly IRuleExecutionService _ruleExecutionService;
    private readonly ILogger<RuleExecutionInterceptor> _logger;

    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        if (context == null) return result;

        // Zpracování entit před uložením
        await ProcessEntitiesBeforeSave(context, cancellationToken);

        return await base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private async Task ProcessEntitiesBeforeSave(DbContext context, CancellationToken cancellationToken)
    {
        foreach (var entry in context.ChangeTracker.Entries())
        {
            var entityName = entry.Entity.GetType().Name;
            var executionMode = GetExecutionModeFromState(entry.State);

            // Najdi pravidla pro tuto entitu a operaci
            var applicableRules = await _ruleExecutionService
                .GetApplicableRulesAsync(entityName, executionMode, cancellationToken);

            // Spusť pravidla
            foreach (var rule in applicableRules)
            {
                await ExecuteRuleOnEntity(rule, entry, cancellationToken);
            }
        }
    }
}
```

### 4. IRuleExecutionService

```csharp
public interface IRuleExecutionService
{
    // Automatické spouštění
    Task<IEnumerable<BusinessRule>> GetApplicableRulesAsync(
        string entityName, 
        RuleExecutionMode mode, 
        CancellationToken cancellationToken = default);
    
    Task<RuleExecutionResult> ExecuteRuleAsync(
        BusinessRule rule, 
        object entity, 
        CancellationToken cancellationToken = default);
    
    // Ruční spouštění
    Task<RuleExecutionResult> ExecuteRuleManuallyAsync(
        Guid ruleId, 
        object entity, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<RuleExecutionResult>> ExecuteAllRulesForEntityAsync(
        object entity, 
        RuleExecutionMode? mode = null, 
        CancellationToken cancellationToken = default);
    
    // Validace
    Task<ValidationResult> ValidateEntityAsync(
        object entity, 
        CancellationToken cancellationToken = default);
}

public class RuleExecutionResult
{
    public Guid RuleId { get; set; }
    public string RuleName { get; set; }
    public bool Success { get; set; }
    public object? Result { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public RuleResultAction Action { get; set; }
    public string? TargetProperty { get; set; }
}
```

## Implementační strategie

### Fáze 1: Základní infrastruktura
1. Rozšíření BusinessRule entity o nové vlastnosti
2. Vytvoření IRuleExecutionService a implementace
3. Registrace v DI kontejneru

### Fáze 2: Automatické spouštění
1. Implementace RuleExecutionInterceptor
2. Registrace interceptoru v ApplicationDbContext
3. Testování na jednoduchých pravidlech

### Fáze 3: Ruční spouštění
1. Rozšíření EntityFacade o rule execution metody
2. Nové API endpointy pro ruční spouštění
3. UI komponenty pro správu pravidel

### Fáze 4: Pokročilé funkce
1. Podmíněné spouštění pravidel
2. Řetězení pravidel (priority)
3. Validační pravidla
4. Monitoring a logování

## Příklady použití

### 1. Automatický výpočet slevy při vytvoření objednávky

```csharp
// Pravidlo se spustí automaticky při vytvoření Order entity
var discountRule = new BusinessRule
{
    Name = "Automatická sleva nad 1000 Kč",
    TargetEntityName = "Order",
    TargetProperty = "DiscountAmount",
    ExecutionMode = RuleExecutionMode.OnCreate,
    ResultAction = RuleResultAction.SetProperty,
    RootNode = new OperationNode
    {
        Operator = OperatorType.If,
        Operands = new List<RuleNode>
        {
            // IF Amount > 1000
            new OperationNode
            {
                Operator = OperatorType.GreaterThan,
                Operands = new List<RuleNode>
                {
                    new SourceValueNode { SourcePath = "Amount" },
                    new ConstantNode { DataType = ValueType.Decimal, Value = "1000" }
                }
            },
            // THEN Amount * 0.1
            new OperationNode
            {
                Operator = OperatorType.Multiply,
                Operands = new List<RuleNode>
                {
                    new SourceValueNode { SourcePath = "Amount" },
                    new ConstantNode { DataType = ValueType.Decimal, Value = "0.1" }
                }
            },
            // ELSE 0
            new ConstantNode { DataType = ValueType.Decimal, Value = "0" }
        }
    }
};
```

### 2. Validace při aktualizaci

```csharp
// Pravidlo validuje, že faktura nemůže být zaplacena před datem splatnosti
var validationRule = new BusinessRule
{
    Name = "Validace data platby",
    TargetEntityName = "Invoice",
    ExecutionMode = RuleExecutionMode.OnUpdate,
    ResultAction = RuleResultAction.Validate,
    RootNode = new OperationNode
    {
        Operator = OperatorType.Or,
        Operands = new List<RuleNode>
        {
            // NOT IsPaid
            new OperationNode
            {
                Operator = OperatorType.Not,
                Operands = new List<RuleNode>
                {
                    new SourceValueNode { SourcePath = "IsPaid" }
                }
            },
            // PaymentDate >= DueDate
            new OperationNode
            {
                Operator = OperatorType.GreaterThanOrEqual,
                Operands = new List<RuleNode>
                {
                    new SourceValueNode { SourcePath = "PaymentDate" },
                    new SourceValueNode { SourcePath = "DueDate" }
                }
            }
        }
    }
};
```

### 3. Ruční spouštění přes EntityFacade

```csharp
public class OrderService
{
    private readonly IEntityFacade _entityFacade;
    private readonly IRuleExecutionService _ruleExecutionService;

    public async Task<Result<object>> CreateOrderWithCustomRulesAsync(OrderAddEdit orderDto)
    {
        // 1. Vytvoř objednávku (automatická pravidla se spustí)
        var createResult = await _entityFacade.CreateAsync<Order>(orderDto);
        if (!createResult.Succeeded) return createResult;

        // 2. Načti vytvořenou objednávku
        var order = await _entityFacade.GetByIdAsync<Order>(createResult.Data);
        if (!order.Succeeded) return Result<object>.Error("Nepodařilo se načíst vytvořenou objednávku");

        // 3. Spusť dodatečná pravidla manuálně
        var ruleResults = await _ruleExecutionService.ExecuteAllRulesForEntityAsync(
            order.Data, 
            RuleExecutionMode.Manual);

        // 4. Zpracuj výsledky pravidel
        foreach (var ruleResult in ruleResults)
        {
            if (ruleResult.Success && ruleResult.Action == RuleResultAction.SetProperty)
            {
                // Aktualizuj vlastnost entity
                var property = order.Data.GetType().GetProperty(ruleResult.TargetProperty);
                property?.SetValue(order.Data, ruleResult.Result);
            }
        }

        // 5. Ulož změny
        await _entityFacade.UpdateAsync<Order>(createResult.Data, order.Data);

        return createResult;
    }
}
```

## Výhody navrhovaného řešení

### 1. Flexibilita
- Podporuje jak automatické, tak ruční spouštění
- Různé typy akcí (nastavení vlastnosti, validace, výpočet)
- Konfigurovatelné podmínky spuštění

### 2. Integrace s existující architekturou
- Využívá existující interceptory
- Kompatibilní s EntityFacade
- Zachovává Clean Architecture principy

### 3. Výkon
- Pravidla se spouštějí pouze když je potřeba
- Cache kompilovaných pravidel
- Možnost nastavení priorit

### 4. Rozšiřitelnost
- Snadné přidání nových typů akcí
- Podpora pro složitější scénáře
- Možnost řetězení pravidel

## Rizika a omezení

### 1. Složitost
- Více konfigurací pro každé pravidlo
- Potřeba pečlivého testování automatických pravidel

### 2. Výkon
- Dodatečná zátěž při každé CRUD operaci
- Nutnost optimalizace pro velké množství pravidel

### 3. Debugging
- Složitější ladění automaticky spouštěných pravidel
- Potřeba dobrého logování a monitoringu

## Doporučení pro implementaci

1. **Začít s manuálním spouštěním** - Jednodušší implementace a testování
2. **Postupně přidat automatické spouštění** - Po ověření stability
3. **Důkladné testování** - Zejména pro automatická pravidla
4. **Monitoring** - Sledování výkonu a chyb
5. **Dokumentace** - Jasné pokyny pro vývojáře a analytiky

---

## Konkrétní implementace

### 1. Rozšíření BusinessRule entity

```csharp
// Infrastructure/RuleEngine/BusinessRule.cs
public class BusinessRule
{
    [Key]
    public Guid Id { get; set; }

    [Timestamp]
    public byte[] RowVersion { get; set; } = Array.Empty<byte>();

    public required string Name { get; set; }
    public string? Description { get; set; }
    public required string TargetEntityName { get; set; }
    public string? TargetProperty { get; set; }
    public string SchemaVersion { get; set; } = "1.0";
    public required RuleNode RootNode { get; set; }
    public bool IsActive { get; set; } = true;

    [NotAudited("Interní poznámky pro vývojáře")]
    public string? InternalNotes { get; set; }

    // Nové vlastnosti pro integraci
    public RuleExecutionMode ExecutionMode { get; set; } = RuleExecutionMode.Manual;
    public RuleResultAction ResultAction { get; set; } = RuleResultAction.SetProperty;
    public int Priority { get; set; } = 0;
    public string? ExecutionCondition { get; set; } // JSON podmínka pro spuštění
}

public enum RuleExecutionMode
{
    Manual = 0,
    OnCreate = 1,
    OnUpdate = 2,
    OnCreateOrUpdate = 3,
    OnDelete = 4,
    OnAnyChange = 7
}

public enum RuleResultAction
{
    SetProperty = 0,
    Validate = 1,
    Calculate = 2,
    Trigger = 3
}
```

### 2. IRuleExecutionService

```csharp
// Application/Services/RuleEngine/IRuleExecutionService.cs
public interface IRuleExecutionService
{
    Task<IEnumerable<BusinessRule>> GetApplicableRulesAsync(
        string entityName,
        RuleExecutionMode mode,
        CancellationToken cancellationToken = default);

    Task<RuleExecutionResult> ExecuteRuleAsync(
        BusinessRule rule,
        object entity,
        CancellationToken cancellationToken = default);

    Task<RuleExecutionResult> ExecuteRuleManuallyAsync(
        Guid ruleId,
        object entity,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<RuleExecutionResult>> ExecuteAllRulesForEntityAsync(
        object entity,
        RuleExecutionMode? mode = null,
        CancellationToken cancellationToken = default);

    Task<EntityValidationResult> ValidateEntityAsync(
        object entity,
        CancellationToken cancellationToken = default);
}

public class RuleExecutionResult
{
    public Guid RuleId { get; set; }
    public string RuleName { get; set; } = string.Empty;
    public bool Success { get; set; }
    public object? Result { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public RuleResultAction Action { get; set; }
    public string? TargetProperty { get; set; }
}

public class EntityValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<RuleExecutionResult> RuleResults { get; set; } = new();
}
```

### 3. Implementace RuleExecutionService

```csharp
// Infrastructure/RuleEngine/Services/RuleExecutionService.cs
public class RuleExecutionService : IRuleExecutionService
{
    private readonly IRuleRepository _ruleRepository;
    private readonly CalculationEngine _calculationEngine;
    private readonly ILogger<RuleExecutionService> _logger;

    public RuleExecutionService(
        IRuleRepository ruleRepository,
        CalculationEngine calculationEngine,
        ILogger<RuleExecutionService> logger)
    {
        _ruleRepository = ruleRepository;
        _calculationEngine = calculationEngine;
        _logger = logger;
    }

    public async Task<IEnumerable<BusinessRule>> GetApplicableRulesAsync(
        string entityName,
        RuleExecutionMode mode,
        CancellationToken cancellationToken = default)
    {
        var allRules = await _ruleRepository.GetActiveRulesForEntityAsync(entityName, cancellationToken);

        return allRules.Where(rule =>
            rule.IsActive &&
            (rule.ExecutionMode == mode ||
             (mode == RuleExecutionMode.OnCreate && rule.ExecutionMode == RuleExecutionMode.OnCreateOrUpdate) ||
             (mode == RuleExecutionMode.OnUpdate && rule.ExecutionMode == RuleExecutionMode.OnCreateOrUpdate) ||
             rule.ExecutionMode == RuleExecutionMode.OnAnyChange))
            .OrderBy(rule => rule.Priority);
    }

    public async Task<RuleExecutionResult> ExecuteRuleAsync(
        BusinessRule rule,
        object entity,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Spouštím pravidlo '{RuleName}' na entitě {EntityType}",
                rule.Name, entity.GetType().Name);

            // Kontrola dodatečné podmínky
            if (!string.IsNullOrEmpty(rule.ExecutionCondition))
            {
                var conditionMet = await EvaluateExecutionCondition(rule.ExecutionCondition, entity);
                if (!conditionMet)
                {
                    _logger.LogDebug("Pravidlo '{RuleName}' přeskočeno - podmínka nesplněna", rule.Name);
                    return new RuleExecutionResult
                    {
                        RuleId = rule.Id,
                        RuleName = rule.Name,
                        Success = true,
                        Result = null,
                        ExecutionTime = stopwatch.Elapsed,
                        Action = rule.ResultAction,
                        TargetProperty = rule.TargetProperty
                    };
                }
            }

            // Spuštění pravidla
            var result = _calculationEngine.Execute(rule, entity);

            stopwatch.Stop();

            _logger.LogDebug("Pravidlo '{RuleName}' úspěšně dokončeno za {ElapsedMs}ms",
                rule.Name, stopwatch.ElapsedMilliseconds);

            return new RuleExecutionResult
            {
                RuleId = rule.Id,
                RuleName = rule.Name,
                Success = true,
                Result = result,
                ExecutionTime = stopwatch.Elapsed,
                Action = rule.ResultAction,
                TargetProperty = rule.TargetProperty
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            _logger.LogError(ex, "Chyba při spouštění pravidla '{RuleName}': {ErrorMessage}",
                rule.Name, ex.Message);

            return new RuleExecutionResult
            {
                RuleId = rule.Id,
                RuleName = rule.Name,
                Success = false,
                ErrorMessage = ex.Message,
                ExecutionTime = stopwatch.Elapsed,
                Action = rule.ResultAction,
                TargetProperty = rule.TargetProperty
            };
        }
    }

    public async Task<RuleExecutionResult> ExecuteRuleManuallyAsync(
        Guid ruleId,
        object entity,
        CancellationToken cancellationToken = default)
    {
        var rule = await _ruleRepository.GetByIdAsync(ruleId);
        if (rule == null)
        {
            return new RuleExecutionResult
            {
                RuleId = ruleId,
                RuleName = "Unknown",
                Success = false,
                ErrorMessage = $"Pravidlo s ID {ruleId} nebylo nalezeno"
            };
        }

        return await ExecuteRuleAsync(rule, entity, cancellationToken);
    }

    public async Task<IEnumerable<RuleExecutionResult>> ExecuteAllRulesForEntityAsync(
        object entity,
        RuleExecutionMode? mode = null,
        CancellationToken cancellationToken = default)
    {
        var entityName = entity.GetType().Name;
        var executionMode = mode ?? RuleExecutionMode.Manual;

        var applicableRules = await GetApplicableRulesAsync(entityName, executionMode, cancellationToken);
        var results = new List<RuleExecutionResult>();

        foreach (var rule in applicableRules)
        {
            var result = await ExecuteRuleAsync(rule, entity, cancellationToken);
            results.Add(result);

            // Pokud je pravidlo validační a selže, přeruš další spouštění
            if (rule.ResultAction == RuleResultAction.Validate && !result.Success)
            {
                _logger.LogWarning("Validační pravidlo '{RuleName}' selhalo, přerušuji další spouštění", rule.Name);
                break;
            }
        }

        return results;
    }

    public async Task<EntityValidationResult> ValidateEntityAsync(
        object entity,
        CancellationToken cancellationToken = default)
    {
        var entityName = entity.GetType().Name;
        var validationRules = await _ruleRepository.GetValidationRulesForEntityAsync(entityName, cancellationToken);

        var validationResult = new EntityValidationResult { IsValid = true };

        foreach (var rule in validationRules.OrderBy(r => r.Priority))
        {
            var result = await ExecuteRuleAsync(rule, entity, cancellationToken);
            validationResult.RuleResults.Add(result);

            if (!result.Success)
            {
                validationResult.IsValid = false;
                validationResult.Errors.Add($"Pravidlo '{rule.Name}': {result.ErrorMessage}");
            }
            else if (result.Result is bool isValid && !isValid)
            {
                validationResult.IsValid = false;
                validationResult.Errors.Add($"Validace '{rule.Name}' selhala");
            }
        }

        return validationResult;
    }

    private async Task<bool> EvaluateExecutionCondition(string condition, object entity)
    {
        // Implementace vyhodnocení podmínky - může být JSON nebo jednoduchý výraz
        // Pro jednoduchost zatím vracíme true
        return await Task.FromResult(true);
    }
}
```

### 4. RuleExecutionInterceptor

```csharp
// Infrastructure/Persistence/Interceptors/RuleExecutionInterceptor.cs
public class RuleExecutionInterceptor : SaveChangesInterceptor
{
    private readonly IRuleExecutionService _ruleExecutionService;
    private readonly ILogger<RuleExecutionInterceptor> _logger;

    public RuleExecutionInterceptor(
        IRuleExecutionService ruleExecutionService,
        ILogger<RuleExecutionInterceptor> logger)
    {
        _ruleExecutionService = ruleExecutionService;
        _logger = logger;
    }

    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        if (context == null) return result;

        await ProcessEntitiesBeforeSave(context, cancellationToken);

        return await base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private async Task ProcessEntitiesBeforeSave(DbContext context, CancellationToken cancellationToken)
    {
        var entitiesToProcess = new List<(EntityEntry Entry, RuleExecutionMode Mode)>();

        // Shromáždíme entity k zpracování
        foreach (var entry in context.ChangeTracker.Entries())
        {
            // Přeskočíme BusinessRule entity aby nedošlo k nekonečné smyčce
            if (entry.Entity is BusinessRule) continue;

            var mode = GetExecutionModeFromState(entry.State);
            if (mode.HasValue)
            {
                entitiesToProcess.Add((entry, mode.Value));
            }
        }

        // Zpracujeme entity
        foreach (var (entry, mode) in entitiesToProcess)
        {
            await ProcessEntityEntry(entry, mode, cancellationToken);
        }
    }

    private async Task ProcessEntityEntry(EntityEntry entry, RuleExecutionMode mode, CancellationToken cancellationToken)
    {
        try
        {
            var entityName = entry.Entity.GetType().Name;
            _logger.LogDebug("Zpracovávám entitu {EntityName} v režimu {Mode}", entityName, mode);

            // Najdi aplikovatelná pravidla
            var applicableRules = await _ruleExecutionService.GetApplicableRulesAsync(entityName, mode, cancellationToken);

            foreach (var rule in applicableRules)
            {
                var result = await _ruleExecutionService.ExecuteRuleAsync(rule, entry.Entity, cancellationToken);

                if (result.Success)
                {
                    await ApplyRuleResult(entry, result);
                }
                else
                {
                    _logger.LogWarning("Pravidlo '{RuleName}' selhalo: {ErrorMessage}",
                        result.RuleName, result.ErrorMessage);

                    // Pro validační pravidla vyhodíme výjimku
                    if (rule.ResultAction == RuleResultAction.Validate)
                    {
                        throw new InvalidOperationException($"Validace selhala: {result.ErrorMessage}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při zpracování entity {EntityType}: {ErrorMessage}",
                entry.Entity.GetType().Name, ex.Message);
            throw;
        }
    }

    private async Task ApplyRuleResult(EntityEntry entry, RuleExecutionResult result)
    {
        switch (result.Action)
        {
            case RuleResultAction.SetProperty:
                if (!string.IsNullOrEmpty(result.TargetProperty) && result.Result != null)
                {
                    var property = entry.Entity.GetType().GetProperty(result.TargetProperty);
                    if (property != null && property.CanWrite)
                    {
                        var convertedValue = Convert.ChangeType(result.Result, property.PropertyType);
                        property.SetValue(entry.Entity, convertedValue);

                        _logger.LogDebug("Nastavena vlastnost {Property} na hodnotu {Value} pro entitu {EntityType}",
                            result.TargetProperty, result.Result, entry.Entity.GetType().Name);
                    }
                }
                break;

            case RuleResultAction.Validate:
                if (result.Result is bool isValid && !isValid)
                {
                    throw new InvalidOperationException($"Validace '{result.RuleName}' selhala");
                }
                break;

            case RuleResultAction.Calculate:
                // Pro výpočty pouze logujeme výsledek
                _logger.LogInformation("Výpočet '{RuleName}' vrátil hodnotu: {Result}",
                    result.RuleName, result.Result);
                break;

            case RuleResultAction.Trigger:
                // Pro triggery můžeme spustit další akce
                _logger.LogInformation("Trigger '{RuleName}' byl spuštěn s výsledkem: {Result}",
                    result.RuleName, result.Result);
                break;
        }

        await Task.CompletedTask;
    }

    private static RuleExecutionMode? GetExecutionModeFromState(EntityState state)
    {
        return state switch
        {
            EntityState.Added => RuleExecutionMode.OnCreate,
            EntityState.Modified => RuleExecutionMode.OnUpdate,
            EntityState.Deleted => RuleExecutionMode.OnDelete,
            _ => null
        };
    }
}
```

### 5. Rozšíření IRuleRepository

```csharp
// Infrastructure/RuleEngine/API/IRuleRepository.cs
public interface IRuleRepository
{
    // Existující metody...
    Task<BusinessRule?> GetByIdAsync(Guid id);
    Task<IEnumerable<BusinessRule>> GetAllAsync();
    Task AddAsync(BusinessRule rule);
    Task UpdateAsync(BusinessRule rule);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsWithNameAsync(string name, Guid? excludeId);
    Task SaveChangesAsync();

    // Nové metody pro integraci
    Task<IEnumerable<BusinessRule>> GetActiveRulesForEntityAsync(
        string entityName,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<BusinessRule>> GetRulesByExecutionModeAsync(
        string entityName,
        RuleExecutionMode mode,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<BusinessRule>> GetValidationRulesForEntityAsync(
        string entityName,
        CancellationToken cancellationToken = default);
}
```

### 6. Registrace v DI

```csharp
// Infrastructure/DependencyInjection.cs
private static void RegisterRuleEngine(IServiceCollection services)
{
    // Existující registrace...
    services.AddScoped<IRuleRepository, RuleRepository>();
    services.AddScoped<IRuleDataProvider, RuleDataProvider>();
    services.AddScoped<CalculationEngine>();

    // Nová registrace
    services.AddScoped<IRuleExecutionService, RuleExecutionService>();

    // Registrace interceptoru
    services.AddScoped<RuleExecutionInterceptor>();
}

// V ApplicationDbContext
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    optionsBuilder.AddInterceptors(
        serviceProvider.GetRequiredService<TrackableEntityInterceptor>(),
        serviceProvider.GetRequiredService<AuditableEntityInterceptor>(),
        serviceProvider.GetRequiredService<RuleExecutionInterceptor>() // Nový interceptor
    );
}
```

Tato implementace poskytuje kompletní řešení pro integraci Rule Enginu s generickou správou entit, podporuje jak automatické, tak ruční spouštění pravidel a je navržena tak, aby byla rozšiřitelná a výkonná.
