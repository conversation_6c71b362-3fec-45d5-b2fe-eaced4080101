# Rule Engine - Praktické příklady integrace

## <PERSON><PERSON><PERSON>ře použití

### 1. Automatický výpočet slevy při vytvoření objednávky

#### Definice pravidla
```json
{
  "name": "Automatická sleva nad 1000 Kč",
  "description": "10% sleva se automaticky aplikuje při vytvoření objednávky nad 1000 Kč",
  "targetEntityName": "Order",
  "targetProperty": "DiscountAmount",
  "executionMode": "OnCreate",
  "resultAction": "SetProperty",
  "priority": 10,
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThan",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "1000" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.1" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

#### Použití v aplikaci
```csharp
// Při vytvoření objednávky se pravidlo spustí automaticky
var orderDto = new OrderAddEdit
{
    Amount = 1500,
    CustomerId = customerId,
    Items = orderItems
};

// Automatické pravidlo se spustí v RuleExecutionInterceptor
var result = await _entityFacade.CreateAsync<Order>(orderDto);

// Výsledná objednávka bude mít DiscountAmount = 150 (10% z 1500)
```

### 2. Validace faktury při aktualizaci

#### Definice validačního pravidla
```json
{
  "name": "Validace data platby faktury",
  "description": "Faktura nemůže být označena jako zaplacená před datem splatnosti",
  "targetEntityName": "Invoice",
  "executionMode": "OnUpdate",
  "resultAction": "Validate",
  "priority": 1,
  "rootNode": {
    "nodeType": "Operation",
    "operator": "Or",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "Not",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "IsPaid" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "GreaterThanOrEqual",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "PaymentDate" },
          { "nodeType": "SourceValue", "sourcePath": "DueDate" }
        ]
      }
    ]
  },
  "isActive": true
}
```

#### Použití v aplikaci
```csharp
// Při pokusu o neplatnou aktualizaci se vyhodí výjimka
var invoiceDto = new InvoiceAddEdit
{
    IsPaid = true,
    PaymentDate = DateTime.Now.AddDays(-10), // Před datem splatnosti
    DueDate = DateTime.Now.AddDays(5)
};

try
{
    await _entityFacade.UpdateAsync<Invoice>(invoiceId, invoiceDto);
}
catch (InvalidOperationException ex)
{
    // "Validace 'Validace data platby faktury' selhala"
    Console.WriteLine(ex.Message);
}
```

### 3. Ruční spouštění pravidel pro výpočet věrnostní slevy

#### Definice pravidla
```json
{
  "name": "Věrnostní sleva podle celkových nákupů",
  "description": "Sleva podle celkové hodnoty nákupů zákazníka za poslední rok",
  "targetEntityName": "Order",
  "targetProperty": "LoyaltyDiscount",
  "executionMode": "Manual",
  "resultAction": "Calculate",
  "priority": 20,
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThan",
        "operands": [
          {
            "nodeType": "Aggregation",
            "aggregationType": "Sum",
            "collectionPath": "Customer.Orders",
            "fieldPath": "Amount",
            "filter": {
              "nodeType": "Operation",
              "operator": "GreaterThan",
              "operands": [
                { "nodeType": "SourceValue", "sourcePath": "OrderDate" },
                { "nodeType": "Constant", "dataType": "DateTime", "value": "2024-01-01" }
              ]
            }
          },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "50000" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.15" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

#### Použití v aplikaci
```csharp
public class OrderService
{
    private readonly IEntityFacade _entityFacade;
    private readonly IRuleExecutionService _ruleExecutionService;

    public async Task<decimal> CalculateLoyaltyDiscountAsync(Guid orderId)
    {
        // Načti objednávku
        var orderResult = await _entityFacade.GetByIdAsync<Order>(orderId);
        if (!orderResult.Succeeded) return 0;

        // Spusť věrnostní pravidlo manuálně
        var ruleResults = await _ruleExecutionService.ExecuteAllRulesForEntityAsync(
            orderResult.Data, 
            RuleExecutionMode.Manual);

        // Najdi výsledek věrnostního pravidla
        var loyaltyRule = ruleResults.FirstOrDefault(r => 
            r.RuleName.Contains("Věrnostní") && r.Success);

        return loyaltyRule?.Result is decimal discount ? discount : 0;
    }

    public async Task<Result<object>> CreateOrderWithLoyaltyDiscountAsync(OrderAddEdit orderDto)
    {
        // 1. Vytvoř objednávku (automatická pravidla se spustí)
        var createResult = await _entityFacade.CreateAsync<Order>(orderDto);
        if (!createResult.Succeeded) return createResult;

        // 2. Načti vytvořenou objednávku s relacemi
        var order = await _entityFacade.GetByIdAsync<Order>(createResult.Data);
        if (!order.Succeeded) return Result<object>.Error("Nepodařilo se načíst objednávku");

        // 3. Spusť manuální pravidla (věrnostní slevy)
        var manualRules = await _ruleExecutionService.ExecuteAllRulesForEntityAsync(
            order.Data, 
            RuleExecutionMode.Manual);

        // 4. Aplikuj výsledky manuálních pravidel
        var hasChanges = false;
        foreach (var ruleResult in manualRules.Where(r => r.Success))
        {
            if (ruleResult.Action == RuleResultAction.SetProperty && 
                !string.IsNullOrEmpty(ruleResult.TargetProperty))
            {
                var property = order.Data.GetType().GetProperty(ruleResult.TargetProperty);
                if (property != null && property.CanWrite)
                {
                    var convertedValue = Convert.ChangeType(ruleResult.Result, property.PropertyType);
                    property.SetValue(order.Data, convertedValue);
                    hasChanges = true;
                }
            }
        }

        // 5. Ulož změny pokud byly nějaké
        if (hasChanges)
        {
            await _entityFacade.UpdateAsync<Order>(createResult.Data, order.Data);
        }

        return createResult;
    }
}
```

## API Endpointy pro ruční spouštění

### 1. Spuštění konkrétního pravidla

```csharp
// Infrastructure/RuleEngine/API/RuleEngineEndpoints.cs
group.MapPost("/rules/{ruleId:guid}/execute", async (
    Guid ruleId,
    [FromBody] object entityData,
    [FromServices] IRuleExecutionService ruleExecutionService) =>
{
    try
    {
        var result = await ruleExecutionService.ExecuteRuleManuallyAsync(ruleId, entityData);
        
        return Results.Ok(new
        {
            ruleId = result.RuleId,
            ruleName = result.RuleName,
            success = result.Success,
            result = result.Result,
            errorMessage = result.ErrorMessage,
            executionTimeMs = result.ExecutionTime.TotalMilliseconds,
            action = result.Action.ToString(),
            targetProperty = result.TargetProperty
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Chyba při spouštění pravidla: {ex.Message}");
    }
})
.WithName("ExecuteRule")
.WithSummary("Spustí konkrétní pravidlo na zadaných datech")
.WithDescription("Umožňuje manuální spuštění pravidla pro testování nebo výpočty")
.Produces(StatusCodes.Status200OK)
.Produces(StatusCodes.Status400BadRequest)
.Produces(StatusCodes.Status500InternalServerError);
```

### 2. Spuštění všech pravidel pro entitu

```csharp
group.MapPost("/entities/{entityName}/execute-rules", async (
    string entityName,
    [FromBody] object entityData,
    [FromQuery] string? mode,
    [FromServices] IRuleExecutionService ruleExecutionService) =>
{
    try
    {
        var executionMode = Enum.TryParse<RuleExecutionMode>(mode, true, out var parsedMode) 
            ? parsedMode 
            : RuleExecutionMode.Manual;

        var results = await ruleExecutionService.ExecuteAllRulesForEntityAsync(entityData, executionMode);
        
        return Results.Ok(new
        {
            entityName,
            executionMode = executionMode.ToString(),
            totalRules = results.Count(),
            successfulRules = results.Count(r => r.Success),
            results = results.Select(r => new
            {
                ruleId = r.RuleId,
                ruleName = r.RuleName,
                success = r.Success,
                result = r.Result,
                errorMessage = r.ErrorMessage,
                executionTimeMs = r.ExecutionTime.TotalMilliseconds,
                action = r.Action.ToString(),
                targetProperty = r.TargetProperty
            })
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Chyba při spouštění pravidel: {ex.Message}");
    }
})
.WithName("ExecuteEntityRules")
.WithSummary("Spustí všechna pravidla pro konkrétní entitu")
.WithDescription("Spustí všechna aktivní pravidla pro zadanou entitu v určeném režimu")
.Produces(StatusCodes.Status200OK)
.Produces(StatusCodes.Status400BadRequest)
.Produces(StatusCodes.Status500InternalServerError);
```

### 3. Validace entity

```csharp
group.MapPost("/entities/{entityName}/validate", async (
    string entityName,
    [FromBody] object entityData,
    [FromServices] IRuleExecutionService ruleExecutionService) =>
{
    try
    {
        var validationResult = await ruleExecutionService.ValidateEntityAsync(entityData);
        
        return Results.Ok(new
        {
            entityName,
            isValid = validationResult.IsValid,
            errors = validationResult.Errors,
            validationRules = validationResult.RuleResults.Select(r => new
            {
                ruleId = r.RuleId,
                ruleName = r.RuleName,
                success = r.Success,
                result = r.Result,
                errorMessage = r.ErrorMessage
            })
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Chyba při validaci entity: {ex.Message}");
    }
})
.WithName("ValidateEntity")
.WithSummary("Validuje entitu pomocí validačních pravidel")
.WithDescription("Spustí všechna validační pravidla pro entitu a vrátí výsledek validace")
.Produces(StatusCodes.Status200OK)
.Produces(StatusCodes.Status400BadRequest)
.Produces(StatusCodes.Status500InternalServerError);
```

## Rozšíření EntityFacade

```csharp
// Application/Features/Generic/Facade/IEntityFacade.cs
public interface IEntityFacade
{
    // Existující metody...
    
    // Nové metody pro rule execution
    Task<RuleExecutionResult> ExecuteRuleAsync<TEntity>(Guid ruleId, TEntity entity)
        where TEntity : class;
    
    Task<IEnumerable<RuleExecutionResult>> ExecuteAllRulesAsync<TEntity>(
        TEntity entity, 
        RuleExecutionMode? mode = null)
        where TEntity : class;
    
    Task<EntityValidationResult> ValidateAsync<TEntity>(TEntity entity)
        where TEntity : class;
}

// Implementace v EntityFacade
public async Task<RuleExecutionResult> ExecuteRuleAsync<TEntity>(Guid ruleId, TEntity entity)
    where TEntity : class
{
    return await _ruleExecutionService.ExecuteRuleManuallyAsync(ruleId, entity);
}

public async Task<IEnumerable<RuleExecutionResult>> ExecuteAllRulesAsync<TEntity>(
    TEntity entity, 
    RuleExecutionMode? mode = null)
    where TEntity : class
{
    return await _ruleExecutionService.ExecuteAllRulesForEntityAsync(entity, mode);
}

public async Task<EntityValidationResult> ValidateAsync<TEntity>(TEntity entity)
    where TEntity : class
{
    return await _ruleExecutionService.ValidateEntityAsync(entity);
}
```

## Testování integrace

```csharp
[Fact]
public async Task CreateOrder_WithAutomaticDiscountRule_ShouldApplyDiscount()
{
    // Arrange
    var orderDto = new OrderAddEdit
    {
        Amount = 1500,
        CustomerId = Guid.NewGuid()
    };

    // Act
    var result = await _entityFacade.CreateAsync<Order>(orderDto);

    // Assert
    Assert.True(result.Succeeded);
    
    var createdOrder = await _entityFacade.GetByIdAsync<Order>(result.Data);
    Assert.True(createdOrder.Succeeded);
    Assert.Equal(150, createdOrder.Data.DiscountAmount); // 10% z 1500
}

[Fact]
public async Task UpdateInvoice_WithInvalidPaymentDate_ShouldThrowException()
{
    // Arrange
    var invoice = new Invoice
    {
        Id = Guid.NewGuid(),
        DueDate = DateTime.Now.AddDays(5),
        IsPaid = false
    };
    
    await _entityFacade.CreateAsync<Invoice>(invoice);

    var updateDto = new InvoiceAddEdit
    {
        IsPaid = true,
        PaymentDate = DateTime.Now.AddDays(-1), // Před datem splatnosti
        DueDate = DateTime.Now.AddDays(5)
    };

    // Act & Assert
    await Assert.ThrowsAsync<InvalidOperationException>(
        () => _entityFacade.UpdateAsync<Invoice>(invoice.Id, updateDto));
}
```

Tato integrace poskytuje kompletní řešení pro automatické i manuální spouštění pravidel s jasným API a flexibilní konfigurací.
