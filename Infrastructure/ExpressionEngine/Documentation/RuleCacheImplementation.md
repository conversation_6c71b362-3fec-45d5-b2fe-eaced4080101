# Rule Engine Cache Implementation

## Přehled

Implementace pokročilého cachování pro Business Rules Engine s cílem výrazně zlepšit výkon při vykonávání a validaci obchodních pravidel.

## Architektura

### IRuleCacheService Interface

```csharp
public interface IRuleCacheService
{
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null);
    Task<T> GetOrSetAsync<T>(string key, Func<T> factory, TimeSpan? expiry = null);
    Task<int> InvalidateAsync(string pattern);
    Task<bool> RemoveAsync(string key);
    Task ClearAsync();
    RuleCacheStatistics GetStatistics();
}
```

### RuleCacheService Implementace

- **Thread-safe in-memory cache** s podporou expirací
- **Pattern-based invalidace** s wildcards (* a ?)
- **<PERSON><PERSON><PERSON>** expirovaných záz<PERSON> ka<PERSON>d<PERSON>ch 5 minut
- **Statistiky cache** pro monitoring (hit rate, miss count, atd.)

## Cachované Komponenty

### 1. CalculationEngine

#### Zkompilovaná pravidla
- **Klíč**: `compiled:{ruleId}:{ruleHash}`
- **Expiraci**: 1 hodina
- **Obsah**: Zkompilované Expression delegates

#### Výsledky validací
- **Klíč**: `validation:{ruleId}:{ruleHash}`
- **Expiraci**: 30 minut
- **Obsah**: RuleValidationResult objekty

### 2. RuleRepository

#### Databázové dotazy
- **GetAllAsync**: `rules:all` (15 minut)
- **GetByIdAsync**: `rules:id:{id}` (30 minut)
- **GetActiveRulesForEntityAsync**: `rules:entity:{entityName}` (20 minut)

## Cache Invalidace

### Automatická invalidace při změnách

```csharp
// Při přidání/aktualizaci/smazání pravidla
await InvalidateRuleCacheAsync(rule);

private async Task InvalidateRuleCacheAsync(BusinessRule rule)
{
    // Invalidace všech cache záznamů pro pravidlo
    await _cacheService.InvalidateAsync($"*:{rule.Id}:*");
    
    // Invalidace cache pro entitu
    await _cacheService.InvalidateAsync($"rules:entity:{rule.TargetEntityName}");
    
    // Invalidace obecné cache
    await _cacheService.InvalidateAsync("rules:all");
}
```

### Pattern-based invalidace

- `*:{ruleId}:*` - Všechny záznamy pro konkrétní pravidlo
- `rules:entity:*` - Všechny záznamy pro entity
- `compiled:*` - Všechna zkompilovaná pravidla
- `validation:*` - Všechny validace

## Výhody Implementace

### 1. Výkon
- **Zkompilovaná pravidla**: Eliminace opakované kompilace Expression trees
- **Databázové dotazy**: Snížení zátěže databáze
- **Validace**: Cache výsledků syntaktické validace

### 2. Škálovatelnost
- **Thread-safe**: Bezpečné použití v multi-threaded prostředí
- **Automatické čištění**: Prevence memory leaks
- **Konfigurovatelné expiraci**: Optimalizace podle potřeb

### 3. Monitoring
- **Statistiky**: Hit rate, miss count, celkový počet záznamů
- **Logging**: Detailní informace o cache operacích
- **Diagnostika**: Sledování výkonu cache

## Zpětná Kompatibilita

### CalculationEngine
```csharp
// Synchronní metody pro zpětnou kompatibilitu
public object Execute(BusinessRule rule, object entity)
public RuleValidationResult ValidateRule(BusinessRule rule)
public void InvalidateRule(Guid ruleId)
public void ClearCache()

// Nové asynchronní metody
public async Task<object> ExecuteAsync(BusinessRule rule, object entity)
public async Task<RuleValidationResult> ValidateRuleAsync(BusinessRule rule)
public async Task InvalidateRuleAsync(Guid ruleId)
public async Task ClearCacheAsync()
```

### Konstruktor
```csharp
// Zpětná kompatibilita - bez cache služby
public CalculationEngine(
    IExpressionBuilder builder,
    IReadOnlyDictionary<string, Type> entityTypeMap,
    ILogger<CalculationEngine> logger)

// Nový konstruktor s cache
public CalculationEngine(
    IExpressionBuilder builder,
    IReadOnlyDictionary<string, Type> entityTypeMap,
    IRuleCacheService cacheService,
    ILogger<CalculationEngine> logger)
```

## Registrace Služeb

```csharp
// V Infrastructure/DependencyInjection.cs
services.AddSingleton<IRuleCacheService, RuleCacheService>();

// Nebo pomocí extension metody
services.AddRuleEngineServices(); // Registruje všechny služby včetně cache
services.AddRuleEngineCache();    // Registruje pouze cache službu
```

## Testování

### RuleCacheServiceTests
- **13 unit testů** pokrývajících všechny aspekty cache
- **Pattern matching** s wildcards
- **Expiraci** a automatické čištění
- **Statistiky** a monitoring
- **Thread safety** a concurrent access

### CalculationEngineTests
- **14 testů** včetně cache funkcionality
- **Zpětná kompatibilita** synchronních metod
- **Asynchronní operace** s cache
- **Invalidace** a cache management

## Budoucí Rozšíření

### Možné vylepšení
1. **Distributed cache** (Redis) pro škálování
2. **Cache warming** při startu aplikace
3. **Adaptive expiration** na základě usage patterns
4. **Compression** pro velké cache objekty
5. **Metrics export** do monitoring systémů

### Konfigurace
```csharp
public class RuleCacheOptions
{
    public TimeSpan DefaultExpiry { get; set; } = TimeSpan.FromMinutes(10);
    public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(5);
    public int MaxCacheSize { get; set; } = 10000;
    public bool EnableStatistics { get; set; } = true;
}
```

## Závěr

Implementace IRuleCacheService poskytuje robustní, škálovatelné a výkonné cachování pro Business Rules Engine. Zachovává zpětnou kompatibilitu a přidává pokročilé funkce pro optimalizaci výkonu a monitoring.
