# Rule Engine - R<PERSON><PERSON><PERSON> průvodce pro vývojáře

## API Endpoints - R<PERSON><PERSON>ý přehled

### Základní CRUD operace
```http
GET    /api/rule-engine/rules           # Seznam všech pravidel
GET    /api/rule-engine/rules/{id}      # Konkrétní pravidlo
POST   /api/rule-engine/rules           # Vytvoření pravidla
PUT    /api/rule-engine/rules/{id}      # Aktualizace pravidla
DELETE /api/rule-engine/rules/{id}      # Smazání pravidla
```

### Metadata a utility
```http
GET /api/rule-engine/metadata                    # Metadata pro UI
GET /api/rule-engine/entities/{name}/properties  # Vlastnosti entity
POST /api/rule-engine/rules/validate             # Validace pravidla
POST /api/rule-engine/rules/{id}/test            # Test pravidla
```

## Datové struktury

### BusinessRule Entity
```typescript
interface BusinessRule {
  id: string;                    // GUID
  rowVersion: string;            // Pro optimistic locking
  name: string;                  // Název pravidla
  description?: string;          // Popis
  targetEntityName: string;      // Cílová entita
  targetProperty?: string;       // Cílová vlastnost
  schemaVersion: string;         // Verze schématu
  rootNode: RuleNode;           // Kořenový uzel
  isActive: boolean;            // Aktivní/neaktivní
  internalNotes?: string;       // Poznámky pro vývojáře
}
```

### RuleNode Types
```typescript
// Základní abstraktní typ
interface RuleNode {
  nodeType: string;
}

// Operační uzel
interface OperationNode extends RuleNode {
  nodeType: "Operation";
  operator: OperatorType;
  operands: RuleNode[];
}

// Konstantní uzel
interface ConstantNode extends RuleNode {
  nodeType: "Constant";
  dataType: "String" | "Integer" | "Decimal" | "Boolean" | "DateTime" | "Guid";
  value: string;
}

// Zdrojový uzel
interface SourceValueNode extends RuleNode {
  nodeType: "SourceValue";
  sourcePath: string;
}

// Agregační uzel
interface AggregationNode extends RuleNode {
  nodeType: "Aggregation";
  aggregationType: "Sum" | "Count" | "Average" | "Min" | "Max";
  collectionPath: string;
  fieldPath?: string;
  filter?: RuleNode;
}
```

### Metadata Types
```typescript
interface RuleMetadata {
  availableEntities: EntityMetadata[];
  availableOperators: OperatorMetadata[];
  availableAggregations: AggregationMetadata[];
  availableValueTypes: ValueTypeMetadata[];
  schemaVersion: string;
}

interface EntityMetadata {
  name: string;
  displayName: string;
  description?: string;
}

interface OperatorMetadata {
  value: string;
  displayName: string;
  category: string;
  description?: string;
}
```

## JavaScript/TypeScript Client

### Základní client třída
```typescript
class RuleEngineClient {
  constructor(private baseUrl: string = '/api/rule-engine') {}

  async getRules(): Promise<BusinessRule[]> {
    const response = await fetch(`${this.baseUrl}/rules`);
    if (!response.ok) throw new Error('Failed to fetch rules');
    return response.json();
  }

  async getRule(id: string): Promise<BusinessRule> {
    const response = await fetch(`${this.baseUrl}/rules/${id}`);
    if (!response.ok) throw new Error('Failed to fetch rule');
    return response.json();
  }

  async createRule(rule: Omit<BusinessRule, 'id' | 'rowVersion'>): Promise<BusinessRule> {
    const response = await fetch(`${this.baseUrl}/rules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(rule)
    });
    if (!response.ok) throw new Error('Failed to create rule');
    return response.json();
  }

  async updateRule(id: string, rule: BusinessRule): Promise<void> {
    const response = await fetch(`${this.baseUrl}/rules/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(rule)
    });
    if (!response.ok) throw new Error('Failed to update rule');
  }

  async deleteRule(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/rules/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete rule');
  }

  async getMetadata(): Promise<RuleMetadata> {
    const response = await fetch(`${this.baseUrl}/metadata`);
    if (!response.ok) throw new Error('Failed to fetch metadata');
    return response.json();
  }

  async validateRule(rule: Partial<BusinessRule>): Promise<ValidationResult> {
    const response = await fetch(`${this.baseUrl}/rules/validate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(rule)
    });
    if (!response.ok) throw new Error('Failed to validate rule');
    return response.json();
  }

  async testRule(id: string, testData: any): Promise<TestResult> {
    const response = await fetch(`${this.baseUrl}/rules/${id}/test`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData)
    });
    if (!response.ok) throw new Error('Failed to test rule');
    return response.json();
  }
}
```

### Rule Builder Utility
```typescript
class RuleBuilder {
  static constant(dataType: string, value: any): ConstantNode {
    return {
      nodeType: "Constant",
      dataType: dataType as any,
      value: value.toString()
    };
  }

  static sourceValue(path: string): SourceValueNode {
    return {
      nodeType: "SourceValue",
      sourcePath: path
    };
  }

  static operation(operator: string, ...operands: RuleNode[]): OperationNode {
    return {
      nodeType: "Operation",
      operator: operator as any,
      operands: operands
    };
  }

  static aggregation(
    type: string, 
    collectionPath: string, 
    fieldPath?: string, 
    filter?: RuleNode
  ): AggregationNode {
    return {
      nodeType: "Aggregation",
      aggregationType: type as any,
      collectionPath,
      fieldPath,
      filter
    };
  }

  // Convenience methods
  static if(condition: RuleNode, thenValue: RuleNode, elseValue: RuleNode): OperationNode {
    return this.operation("If", condition, thenValue, elseValue);
  }

  static greaterThan(left: RuleNode, right: RuleNode): OperationNode {
    return this.operation("GreaterThan", left, right);
  }

  static multiply(left: RuleNode, right: RuleNode): OperationNode {
    return this.operation("Multiply", left, right);
  }

  static add(left: RuleNode, right: RuleNode): OperationNode {
    return this.operation("Add", left, right);
  }
}
```

### Příklady použití

#### Vytvoření jednoduchého pravidla
```typescript
const client = new RuleEngineClient();

// Pravidlo: 10% sleva nad 1000 Kč
const discountRule: Omit<BusinessRule, 'id' | 'rowVersion'> = {
  name: "Sleva nad 1000 Kč",
  description: "10% sleva pro objednávky nad 1000 Kč",
  targetEntityName: "Order",
  targetProperty: "DiscountAmount",
  schemaVersion: "1.0",
  rootNode: RuleBuilder.if(
    RuleBuilder.greaterThan(
      RuleBuilder.sourceValue("Amount"),
      RuleBuilder.constant("Decimal", "1000")
    ),
    RuleBuilder.multiply(
      RuleBuilder.sourceValue("Amount"),
      RuleBuilder.constant("Decimal", "0.1")
    ),
    RuleBuilder.constant("Decimal", "0")
  ),
  isActive: true
};

// Vytvoření pravidla
const createdRule = await client.createRule(discountRule);
console.log('Pravidlo vytvořeno:', createdRule);
```

#### Testování pravidla
```typescript
// Testovací data
const testData = {
  id: "123e4567-e89b-12d3-a456-426614174000",
  amount: 1500,
  customerId: "customer-123"
};

// Test pravidla
const testResult = await client.testRule(createdRule.id, testData);
console.log('Výsledek testu:', testResult.result); // Očekáváno: 150
```

#### Načtení metadat pro UI
```typescript
const metadata = await client.getMetadata();

// Vytvoření dropdown pro entity
const entityOptions = metadata.availableEntities.map(entity => ({
  value: entity.name,
  label: entity.displayName,
  description: entity.description
}));

// Vytvoření dropdown pro operátory
const operatorOptions = metadata.availableOperators.map(op => ({
  value: op.value,
  label: op.displayName,
  category: op.category
}));
```

## React Hooks

### useRuleEngine Hook
```typescript
import { useState, useEffect } from 'react';

export function useRuleEngine() {
  const [client] = useState(() => new RuleEngineClient());
  const [rules, setRules] = useState<BusinessRule[]>([]);
  const [metadata, setMetadata] = useState<RuleMetadata | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadRules = async () => {
    try {
      setLoading(true);
      setError(null);
      const rulesData = await client.getRules();
      setRules(rulesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const loadMetadata = async () => {
    try {
      const metadataData = await client.getMetadata();
      setMetadata(metadataData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load metadata');
    }
  };

  const createRule = async (rule: Omit<BusinessRule, 'id' | 'rowVersion'>) => {
    try {
      setError(null);
      const newRule = await client.createRule(rule);
      setRules(prev => [...prev, newRule]);
      return newRule;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create rule');
      throw err;
    }
  };

  const updateRule = async (id: string, rule: BusinessRule) => {
    try {
      setError(null);
      await client.updateRule(id, rule);
      setRules(prev => prev.map(r => r.id === id ? rule : r));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update rule');
      throw err;
    }
  };

  const deleteRule = async (id: string) => {
    try {
      setError(null);
      await client.deleteRule(id);
      setRules(prev => prev.filter(r => r.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete rule');
      throw err;
    }
  };

  useEffect(() => {
    loadRules();
    loadMetadata();
  }, []);

  return {
    rules,
    metadata,
    loading,
    error,
    createRule,
    updateRule,
    deleteRule,
    refreshRules: loadRules,
    client
  };
}
```

### Použití v komponentě
```typescript
import React from 'react';
import { useRuleEngine } from './hooks/useRuleEngine';

export const RulesList: React.FC = () => {
  const { rules, loading, error, deleteRule } = useRuleEngine();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Obchodní pravidla</h2>
      {rules.map(rule => (
        <div key={rule.id} className="rule-item">
          <h3>{rule.name}</h3>
          <p>{rule.description}</p>
          <p>Entita: {rule.targetEntityName}</p>
          <p>Aktivní: {rule.isActive ? 'Ano' : 'Ne'}</p>
          <button onClick={() => deleteRule(rule.id)}>
            Smazat
          </button>
        </div>
      ))}
    </div>
  );
};
```

## Error Handling

### Standardní error typy
```typescript
interface ApiError {
  message: string;
  details?: string;
  statusCode: number;
}

interface ValidationError {
  message: string;
  errors: string[];
}

// Error handling utility
class RuleEngineError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'RuleEngineError';
  }
}

// Enhanced client with better error handling
class EnhancedRuleEngineClient extends RuleEngineClient {
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new RuleEngineError(
        errorData.message || `HTTP ${response.status}`,
        response.status,
        errorData
      );
    }
    return response.json();
  }

  async getRules(): Promise<BusinessRule[]> {
    const response = await fetch(`${this.baseUrl}/rules`);
    return this.handleResponse<BusinessRule[]>(response);
  }

  // ... další metody s lepším error handlingem
}
```

## Debugging a Monitoring

### Debug utility
```typescript
class RuleDebugger {
  static logRuleStructure(rule: BusinessRule) {
    console.group(`Rule: ${rule.name}`);
    console.log('Target:', rule.targetEntityName);
    console.log('Active:', rule.isActive);
    console.log('Structure:');
    this.logNode(rule.rootNode, 0);
    console.groupEnd();
  }

  private static logNode(node: RuleNode, depth: number) {
    const indent = '  '.repeat(depth);
    console.log(`${indent}${node.nodeType}:`);
    
    switch (node.nodeType) {
      case 'Operation':
        const opNode = node as OperationNode;
        console.log(`${indent}  Operator: ${opNode.operator}`);
        opNode.operands.forEach((operand, i) => {
          console.log(`${indent}  Operand ${i + 1}:`);
          this.logNode(operand, depth + 2);
        });
        break;
      case 'Constant':
        const constNode = node as ConstantNode;
        console.log(`${indent}  Type: ${constNode.dataType}`);
        console.log(`${indent}  Value: ${constNode.value}`);
        break;
      case 'SourceValue':
        const srcNode = node as SourceValueNode;
        console.log(`${indent}  Path: ${srcNode.sourcePath}`);
        break;
    }
  }
}
```

## Performance Tips

1. **Cache metadata**: Metadata se mění zřídka, cachujte je
2. **Batch operations**: Při práci s více pravidly používejte batch operace
3. **Lazy loading**: Načítejte pravidla až když jsou potřeba
4. **Debounce validation**: Při editaci pravidel debounce validaci
5. **Optimistic updates**: Pro lepší UX používejte optimistic updates

## Užitečné odkazy

- [Kompletní dokumentace](./RuleEngineComprehensiveGuide.md)
- [Průvodce pro analytiky](./AnalystQuickReference.md)
- [API dokumentace](./RuleEngineApiDocumentation.md)
