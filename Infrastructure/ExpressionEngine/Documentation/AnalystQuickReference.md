# Rule Engine - Rychlý průvodce pro analytiky

## Základn<PERSON> koncepty

### Co je Rule Engine?
Systém pro vytváření obchodních pravidel bez programování. Umožňuje definovat logiku typu:
- "Pokud objednávka > 1000 Kč, uděl 10% slevu"
- "Pokud zákazník nakoupil za více než 50 000 Kč za rok, je VIP"
- "Pokud faktura není zaplacena 30 dní po splatnosti, pošli upomínku"

### Základní struktura pravidla
```
NÁZEV: Sleva nad 1000 Kč
POPIS: 10% sleva pro velké objednávky
ENTITA: Order (objednávka)
VLASTNOST: DiscountAmount (částka slevy)
LOGIKA: IF Amount > 1000 THEN Amount * 0.1 ELSE 0
```

## Dostupné entity a jejich vlastnosti

### Order (Objednávka)
- `Amount` - částka objednávky
- `TotalAmount` - celková částka včetně DPH
- `OrderDate` - datum objednávky
- `CustomerId` - ID zákazníka
- `Items` - kolekce položek objednávky
- `Status` - stav objednávky

### OrderItem (Položka objednávky)
- `Quantity` - množství
- `Price` - cena za kus
- `ProductId` - ID produktu
- `TotalPrice` - celková cena položky

### Invoice (Faktura)
- `Amount` - částka faktury
- `DueDate` - datum splatnosti
- `IsPaid` - zda je zaplacena
- `PaymentDate` - datum zaplacení
- `CustomerId` - ID zákazníka

### Customer (Zákazník)
- `Name` - jméno
- `Email` - email
- `RegistrationDate` - datum registrace
- `CustomerType` - typ zákazníka (Standard, VIP, ...)
- `Orders` - kolekce objednávek zákazníka

## Typy operací

### Aritmetické operace
- **Sčítání (+)**: `Amount + 100`
- **Odčítání (-)**: `Amount - 50`
- **Násobení (*)**: `Amount * 0.1`
- **Dělení (/)**: `Amount / 2`

### Porovnání
- **Rovná se (==)**: `Amount == 1000`
- **Nerovná se (!=)**: `Status != "Cancelled"`
- **Větší než (>)**: `Amount > 1000`
- **Menší než (<)**: `Amount < 500`
- **Větší nebo rovno (>=)**: `Amount >= 1000`
- **Menší nebo rovno (<=)**: `Amount <= 500`

### Logické operace
- **A zároveň (AND)**: `Amount > 1000 AND CustomerType == "VIP"`
- **Nebo (OR)**: `Amount > 5000 OR CustomerType == "VIP"`
- **Negace (NOT)**: `NOT IsPaid`

### Podmínky (IF-THEN-ELSE)
```
IF podmínka THEN hodnota_pokud_pravda ELSE hodnota_pokud_nepravda
```

Příklad:
```
IF Amount > 1000 THEN Amount * 0.1 ELSE 0
```

### Agregace (práce s kolekcemi)
- **SOUČET (SUM)**: `SUM(Items.Price)` - součet cen všech položek
- **POČET (COUNT)**: `COUNT(Items)` - počet položek
- **PRŮMĚR (AVERAGE)**: `AVERAGE(Items.Price)` - průměrná cena
- **MINIMUM (MIN)**: `MIN(Items.Price)` - nejnižší cena
- **MAXIMUM (MAX)**: `MAX(Items.Price)` - nejvyšší cena

## Časté business vzory

### 1. Jednoduché slevy
```
Název: Sleva nad 1000 Kč
Logika: IF Amount > 1000 THEN Amount * 0.1 ELSE 0
Výsledek: 10% sleva pro objednávky nad 1000 Kč
```

### 2. Stupňovité slevy
```
Název: Stupňovitá sleva
Logika: 
  IF Amount > 5000 THEN Amount * 0.2
  ELSE IF Amount > 2000 THEN Amount * 0.15  
  ELSE IF Amount > 1000 THEN Amount * 0.1
  ELSE 0
Výsledek: 20% nad 5000, 15% nad 2000, 10% nad 1000
```

### 3. VIP zákazníci
```
Název: VIP sleva
Logika: IF CustomerType == "VIP" THEN Amount * 0.15 ELSE 0
Výsledek: 15% sleva pro VIP zákazníky
```

### 4. Kombinované podmínky
```
Název: Kombinovaná sleva
Logika: IF (Amount > 1000 AND CustomerType == "VIP") OR Amount > 5000 
        THEN Amount * 0.2 ELSE 0
Výsledek: 20% sleva pro VIP nad 1000 Kč nebo pro všechny nad 5000 Kč
```

### 5. Množstevní slevy
```
Název: Množstevní sleva
Logika: IF SUM(Items.Quantity) >= 10 THEN Amount * 0.05 ELSE 0
Výsledek: 5% sleva při nákupu 10+ kusů
```

### 6. Věrnostní program
```
Název: Věrnostní sleva
Logika: IF SUM(Customer.Orders.Amount) > 50000 THEN Amount * 0.2
        ELSE IF SUM(Customer.Orders.Amount) > 20000 THEN Amount * 0.1
        ELSE 0
Výsledek: Sleva podle celkové hodnoty nákupů zákazníka
```

### 7. Doprava zdarma
```
Název: Doprava zdarma
Logika: IF Amount >= 15000 THEN 0 ELSE StandardShippingCost
Výsledek: Doprava zdarma pro objednávky nad 15 000 Kč
```

### 8. Sezónní akce
```
Název: Vánoční sleva
Logika: IF MONTH(OrderDate) == 12 THEN Amount * 0.15 ELSE 0
Výsledek: 15% sleva v prosinci
```

## Testovací scénáře

Pro každé pravidlo připravte testovací případy:

### Příklad: Sleva nad 1000 Kč

| Scénář | Částka | Očekávaná sleva | Poznámka |
|--------|--------|----------------|----------|
| Standardní | 1500 Kč | 150 Kč | 10% z 1500 |
| Hranice | 1000 Kč | 0 Kč | Přesně na hranici |
| Pod hranicí | 999 Kč | 0 Kč | Těsně pod hranicí |
| Vysoká částka | 10000 Kč | 1000 Kč | 10% z 10000 |
| Nulová částka | 0 Kč | 0 Kč | Krajní případ |

## Checklist pro vytvoření pravidla

### Před vytvořením
- [ ] Jasně definovaný business požadavek
- [ ] Identifikované vstupní entity a vlastnosti
- [ ] Definované výstupní hodnoty
- [ ] Připravené testovací scénáře

### Při vytváření
- [ ] Popisný název pravidla
- [ ] Detailní popis účelu
- [ ] Správně vybraná cílová entita
- [ ] Logická struktura pravidla
- [ ] Ověření syntaxe

### Po vytvoření
- [ ] Otestování na připravených scénářích
- [ ] Dokumentace pravidla
- [ ] Aktivace pravidla
- [ ] Monitoring výsledků

## Časté chyby a jak se jim vyhnout

### 1. Nesprávné hranice
❌ **Špatně**: `Amount > 1000` (nezahrnuje přesně 1000)
✅ **Správně**: `Amount >= 1000` (zahrnuje 1000)

### 2. Zapomenuté ELSE větve
❌ **Špatně**: `IF Amount > 1000 THEN Amount * 0.1`
✅ **Správně**: `IF Amount > 1000 THEN Amount * 0.1 ELSE 0`

### 3. Nekompatibilní datové typy
❌ **Špatně**: Porovnání textu s číslem
✅ **Správně**: Porovnání stejných typů dat

### 4. Složité vnořené podmínky
❌ **Špatně**: Příliš mnoho vnořených IF-THEN-ELSE
✅ **Správně**: Rozdělení do více jednodušších pravidel

### 5. Nedostatečné testování
❌ **Špatně**: Test pouze na jednom scénáři
✅ **Správně**: Test na hraničních případech a extrémních hodnotách

## Kontakty a podpora

- **Technická podpora**: Vývojový tým
- **Business podpora**: Produktový tým
- **Dokumentace**: `/api/rule-engine/metadata` pro aktuální možnosti
- **Testování**: Použijte endpoint `/api/rule-engine/rules/{id}/test`

## Užitečné odkazy

- [Kompletní dokumentace](./RuleEngineComprehensiveGuide.md)
- [API dokumentace](./RuleEngineApiDocumentation.md)
- [Příklady pravidel](./RuleExamples.md)
