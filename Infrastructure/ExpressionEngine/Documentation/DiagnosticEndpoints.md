# ExpressionEngine - Diagnostické Endpointy

Tento dokument popisuje nové diagnostické endpointy pro testování funkčnosti ExpressionEngine s reálnými entitami.

## Přehled

Diagnostické endpointy umožňují:
- Testování výrazů s reálnými daty z databáze
- Získání ukázkových entit pro testování
- Ověření, že ExpressionEngine správně funguje s business entitami

## Endpointy

### 1. GET /api/expression-engine/sample-entities

Získá ukázkové SampleEntity z databáze pro testování výrazů.

**Parametry:**
- `take` (query, optional): Počet entit k vrácení (výchozí: 5)

**Response:**
```json
{
  "entityType": "SampleEntity",
  "count": 3,
  "entities": [
    {
      "id": 1,
      "name": "<PERSON>",
      "description": "Testovací entita",
      "age": 30,
      "dateOfBirth": "1993-05-15T00:00:00",
      "isActive": true,
      "internalNotes": "Poznámka pro vývojáře",
      "createdAt": "2025-08-03T10:00:00Z",
      "createdBy": "system",
      "modifiedAt": null,
      "modifiedBy": null,
      "rowVersion": "AAAAAAAAAAE="
    }
  ]
}
```

**Příklad použití:**
```bash
curl -k "https://localhost:7003/api/expression-engine/sample-entities?take=3"
```

### 2. POST /api/expression-engine/expressions/{id}/test-with-entity

Testuje konkrétní výraz s poskytnutou entitou a vrací výsledek s diagnostickými informacemi.

**Parametry:**
- `id` (path, required): GUID identifikátor výrazu

**Request Body:**
```json
{
  "entityType": "SampleEntity",
  "entityData": {
    "id": 1,
    "name": "Jan Novák",
    "age": 30,
    "dateOfBirth": "1993-05-15T00:00:00",
    "isActive": true
  }
}
```

**Response (úspěch):**
```json
{
  "expressionId": "123e4567-e89b-12d3-a456-************",
  "expressionName": "Výpočet věku v letech",
  "entityType": "SampleEntity",
  "result": 40,
  "executionTimeMs": 12.5,
  "success": true,
  "errorMessage": null
}
```

**Response (chyba):**
```json
{
  "expressionId": "123e4567-e89b-12d3-a456-************",
  "expressionName": null,
  "entityType": "SampleEntity",
  "result": null,
  "executionTimeMs": 0,
  "success": false,
  "errorMessage": "Chyba při vykonávání výrazu: Property 'InvalidProperty' not found"
}
```

**Příklad použití:**
```bash
curl -k -X POST "https://localhost:7003/api/expression-engine/expressions/{expression-id}/test-with-entity" \
  -H "Content-Type: application/json" \
  -d '{
    "entityType": "SampleEntity",
    "entityData": {
      "id": 1,
      "name": "Jan Novák",
      "age": 30,
      "isActive": true
    }
  }'
```

## Pracovní postup pro testování

### 1. Získání ukázkových dat
```bash
# Získej ukázkové entity z databáze
curl -k "https://localhost:7003/api/expression-engine/sample-entities?take=1"
```

### 2. Vytvoření výrazu
```bash
# Vytvoř nový výraz
curl -k -X POST "https://localhost:7003/api/expression-engine/expressions" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Age Plus Ten",
    "description": "Přidá 10 k věku entity",
    "targetEntityName": "SampleEntity",
    "rootNode": {
      "nodeType": "Operation",
      "operator": "Add",
      "operands": [
        {
          "nodeType": "SourceValue",
          "sourcePath": "Age"
        },
        {
          "nodeType": "Constant",
          "dataType": "Integer",
          "value": "10"
        }
      ]
    }
  }'
```

### 3. Testování výrazu
```bash
# Testuj výraz s reálnou entitou
curl -k -X POST "https://localhost:7003/api/expression-engine/expressions/{expression-id}/test-with-entity" \
  -H "Content-Type: application/json" \
  -d '{
    "entityType": "SampleEntity",
    "entityData": {
      "id": 1,
      "name": "Jan Novák",
      "age": 30,
      "isActive": true
    }
  }'
```

## Podporované typy entit

Aktuálně podporované typy entit pro testování:
- `SampleEntity` - Ukázková business entita s různými datovými typy

## Chybové stavy

### 404 Not Found
- Výraz s daným ID neexistuje

### 400 Bad Request
- Neplatný JSON v request body
- Nepodporovaný typ entity
- Chyba při deserializaci entity dat

### 500 Internal Server Error
- Chyba při vykonávání výrazu
- Databázová chyba

## Bezpečnost

- Endpointy jsou dostupné pouze v development prostředí
- Validace vstupních dat proti známým typům entit
- Bezpečné vykonávání výrazů s error handlingem

## Rozšíření

Pro přidání podpory nových typů entit:

1. Přidej nový case do switch výrazu v `TestExpressionWithEntity` endpointu
2. Implementuj deserializaci pro nový typ entity
3. Aktualizuj dokumentaci

Příklad:
```csharp
entity = request.EntityType.ToLowerInvariant() switch
{
    "sampleentity" => JsonSerializer.Deserialize<SampleEntity>(request.EntityData, options),
    "newentity" => JsonSerializer.Deserialize<NewEntity>(request.EntityData, options),
    _ => throw new ArgumentException($"Nepodporovaný typ entity: {request.EntityType}")
};
```
