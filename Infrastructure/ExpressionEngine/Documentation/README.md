# Rule Engine - Dokumentace

Vítejte v dokumentaci Rule Engine systému. Tento systém umožňuje vytváření, správu a vykonávání obchodních pravidel za běhu aplikace bez nutnosti změn v kódu.

## 📚 Dokumentace podle rolí

### 👨‍💼 Pro analytiky a business uživatele
- **[Rychlý průvodce pro analytiky](./AnalystQuickReference.md)** - Jak navrhovat a testovat obchodní pravidla
- **[Praktické příklady pravidel](./RuleExamples.md)** - Hotové příklady pro časté business scénáře

### 👨‍💻 Pro vývojáře frontendu
- **[Rychlý průvodce pro vývojáře](./DeveloperQuickReference.md)** - API, TypeScript typy, React hooks
- **[Kompletní technická dokumentace](./RuleEngineComprehensiveGuide.md)** - Detailní popis architektury a použití

### 🔧 Pro backend vývojáře
- **[API dokumentace](./RuleEngineApiDocumentation.md)** - Detailní popis všech endpointů
- **[Kompletní technická dokumentace](./RuleEngineComprehensiveGuide.md)** - Architektura a implementační detaily
- **[Integrace s generickou správou entit](./RuleEngineIntegrationDesign.md)** - Návrh automatického a manuálního spouštění pravidel

## 🚀 Rychlý start

### Pro analytiky
1. Přečtěte si [Rychlý průvodce pro analytiky](./AnalystQuickReference.md)
2. Prohlédněte si [Praktické příklady](./RuleExamples.md)
3. Začněte s jednoduchými pravidly typu "sleva nad X Kč"
4. Postupně přidávejte složitější logiku

### Pro vývojáře frontendu
1. Prostudujte [API reference](./DeveloperQuickReference.md)
2. Implementujte základní CRUD operace pro pravidla
3. Přidejte metadata API pro dynamické UI
4. Implementujte validaci a testování pravidel

### Pro backend vývojáře
1. Prohlédněte si [API dokumentaci](./RuleEngineApiDocumentation.md)
2. Prostudujte architekturu v [Kompletní dokumentaci](./RuleEngineComprehensiveGuide.md)
3. Implementujte integraci s vašimi entitami
4. Přidejte monitoring a logování

## 🏗️ Architektura systému

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   API Layer     │    │  Rule Engine    │
│                 │    │                 │    │                 │
│ • Rule Builder  │◄──►│ • REST API      │◄──►│ • BusinessRule  │
│ • Rule List     │    │ • Validation    │    │ • RuleNode      │
│ • Test Runner   │    │ • Metadata      │    │ • Calculator    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │    Database     │
                       │                 │
                       │ • Rules Storage │
                       │ • Metadata      │
                       │ • Audit Trail   │
                       └─────────────────┘
```

## 🔑 Klíčové koncepty

### BusinessRule
Technická entita reprezentující obchodní pravidlo:
- **Name**: Název pravidla
- **Description**: Popis účelu
- **TargetEntityName**: Cílová entita (Order, Invoice, ...)
- **TargetProperty**: Cílová vlastnost
- **RootNode**: Kořenový uzel stromu logiky
- **IsActive**: Aktivní/neaktivní stav

### RuleNode
Stromová struktura reprezentující logiku pravidla:
- **OperationNode**: Operace (IF-THEN-ELSE, aritmetické, logické)
- **ConstantNode**: Konstantní hodnoty
- **SourceValueNode**: Přístup k vlastnostem entit
- **AggregationNode**: Agregace nad kolekcemi (SUM, COUNT, ...)

### CalculationEngine
Jádro systému pro kompilaci a vykonávání pravidel:
- Kompiluje pravidla do Expression Trees
- Cachuje kompilovaná pravidla
- Validuje syntaktickou správnost
- Poskytuje bezpečné vykonávání

## 📊 Podporované operace

### Aritmetické
- Sčítání (+), Odčítání (-), Násobení (*), Dělení (/)

### Porovnávací
- Rovná se (==), Nerovná se (!=)
- Větší než (>), Menší než (<)
- Větší nebo rovno (>=), Menší nebo rovno (<=)

### Logické
- A zároveň (AND), Nebo (OR), Negace (NOT)

### Podmíněné
- IF-THEN-ELSE struktury

### Agregační
- SUM, COUNT, AVERAGE, MIN, MAX nad kolekcemi

## 🎯 Časté use cases

### E-commerce slevy
- Procentní slevy podle výše objednávky
- VIP slevy pro speciální zákazníky
- Množstevní slevy podle počtu kusů
- Doprava zdarma nad určitou částku

### Věrnostní programy
- Slevy podle celkové hodnoty nákupů
- Stupňovité slevy podle historie zákazníka
- Bonusy za opakované nákupy

### Faktury a platby
- Upomínky po splatnosti
- Slevy za rychlou platbu
- Penále za pozdní platbu

### Automatizace procesů
- Automatické schvalování objednávek
- Eskalace neuhrazených faktur
- Notifikace pro kritické stavy

## 🔧 API Endpoints - Přehled

```http
# Správa pravidel
GET    /api/rule-engine/rules           # Seznam pravidel
GET    /api/rule-engine/rules/{id}      # Detail pravidla
POST   /api/rule-engine/rules           # Vytvoření pravidla
PUT    /api/rule-engine/rules/{id}      # Aktualizace pravidla
DELETE /api/rule-engine/rules/{id}      # Smazání pravidla

# Metadata a utility
GET  /api/rule-engine/metadata                    # Metadata pro UI
GET  /api/rule-engine/entities/{name}/properties  # Vlastnosti entity
POST /api/rule-engine/rules/validate              # Validace pravidla
POST /api/rule-engine/rules/{id}/test             # Test pravidla
```

## 📝 Příklad jednoduchého pravidla

**Business požadavek**: 10% sleva pro objednávky nad 1000 Kč

**JavaScript/TypeScript**:
```typescript
const rule = {
  name: "Sleva 10% nad 1000 Kč",
  description: "Základní sleva pro větší objednávky",
  targetEntityName: "Order",
  targetProperty: "DiscountAmount",
  rootNode: {
    nodeType: "Operation",
    operator: "If",
    operands: [
      // Podmínka: Amount > 1000
      {
        nodeType: "Operation",
        operator: "GreaterThan",
        operands: [
          { nodeType: "SourceValue", sourcePath: "Amount" },
          { nodeType: "Constant", dataType: "Decimal", value: "1000" }
        ]
      },
      // THEN: Amount * 0.1
      {
        nodeType: "Operation",
        operator: "Multiply",
        operands: [
          { nodeType: "SourceValue", sourcePath: "Amount" },
          { nodeType: "Constant", dataType: "Decimal", value: "0.1" }
        ]
      },
      // ELSE: 0
      { nodeType: "Constant", dataType: "Decimal", value: "0" }
    ]
  },
  isActive: true
};
```

## 🧪 Testování

```typescript
// Testovací data
const testData = {
  id: "123e4567-e89b-12d3-a456-426614174000",
  amount: 1500,
  customerId: "customer-123"
};

// Test pravidla
const result = await ruleEngineClient.testRule(ruleId, testData);
console.log('Výsledek:', result); // Očekáváno: 150 (10% z 1500)
```

## 🚨 Nejčastější chyby

1. **Nesprávné hranice**: Použití `>` místo `>=`
2. **Zapomenuté ELSE větve**: Neúplné IF-THEN-ELSE struktury
3. **Nekompatibilní datové typy**: Porovnání textu s číslem
4. **Složité vnořené podmínky**: Příliš komplexní logika
5. **Nedostatečné testování**: Test pouze na jednom scénáři

## 🔗 Integrace s aplikací

### 📋 Plánování integrace
- **[Návrh integrace](./RuleEngineIntegrationDesign.md)** - Kompletní návrh automatického a manuálního spouštění
- **[Praktické příklady integrace](./RuleEngineIntegrationExamples.md)** - Konkrétní scénáře použití
- **[Shrnutí integrace](./RuleEngineIntegrationSummary.md)** - Přehled řešení a implementační plán

### 🔄 Způsoby spouštění pravidel

#### Automatické spouštění
```csharp
// Pravidlo se spustí automaticky při vytvoření objednávky
var rule = new BusinessRule
{
    ExecutionMode = RuleExecutionMode.OnCreate,
    ResultAction = RuleResultAction.SetProperty,
    TargetProperty = "DiscountAmount"
};
```

#### Ruční spouštění
```csharp
// Spuštění pravidla na vyžádání
var result = await _ruleExecutionService.ExecuteRuleManuallyAsync(ruleId, entity);

// Spuštění všech pravidel pro entitu
var results = await _entityFacade.ExecuteAllRulesAsync(order, RuleExecutionMode.Manual);
```

## 📚 Užitečné odkazy

- [Kompletní dokumentace](./RuleEngineComprehensiveGuide.md)
- [API dokumentace](./RuleEngineApiDocumentation.md)
- [Příklady pravidel](./RuleExamples.md)
- [Integrace s aplikací](./RuleEngineIntegrationDesign.md)

## 📞 Podpora

- **Technická podpora**: Vývojový tým
- **Business podpora**: Produktový tým
- **Dokumentace**: Aktuální metadata na `/api/rule-engine/metadata`
- **Issues**: GitHub Issues v repository

## 🔄 Aktualizace dokumentace

Tato dokumentace je udržována spolu s kódem. Při změnách v API nebo funkcionalitě prosím aktualizujte odpovídající dokumentační soubory.

**Poslední aktualizace**: Prosinec 2024
**Verze Rule Engine**: 1.0
**Verze API**: 1.0
