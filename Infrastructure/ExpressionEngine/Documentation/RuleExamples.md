# Rule Engine - Prak<PERSON>é příklady pravidel

## Základn<PERSON> slevy

### 1. Jednoduchá procentní sleva
**Business požadavek**: 10% sleva pro objednávky nad 1000 Kč

```json
{
  "name": "Sleva 10% nad 1000 Kč",
  "description": "Základní sleva pro větší objednávky",
  "targetEntityName": "Order",
  "targetProperty": "DiscountAmount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThan",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "1000" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.1" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

### 2. Fixní sleva
**Business požadavek**: 200 Kč sleva pro objednávky nad 2000 Kč

```json
{
  "name": "Fixní sleva 200 Kč",
  "description": "Pevná sleva 200 Kč pro objednávky nad 2000 Kč",
  "targetEntityName": "Order",
  "targetProperty": "DiscountAmount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThan",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "2000" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "200" },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

## Stupňovité slevy

### 3. Třístupňová sleva
**Business požadavek**: 5% nad 1000 Kč, 10% nad 3000 Kč, 15% nad 5000 Kč

```json
{
  "name": "Stupňovitá sleva",
  "description": "Sleva podle výše objednávky: 5%, 10%, 15%",
  "targetEntityName": "Order",
  "targetProperty": "DiscountAmount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThan",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "5000" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.15" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "If",
        "operands": [
          {
            "nodeType": "Operation",
            "operator": "GreaterThan",
            "operands": [
              { "nodeType": "SourceValue", "sourcePath": "Amount" },
              { "nodeType": "Constant", "dataType": "Decimal", "value": "3000" }
            ]
          },
          {
            "nodeType": "Operation",
            "operator": "Multiply",
            "operands": [
              { "nodeType": "SourceValue", "sourcePath": "Amount" },
              { "nodeType": "Constant", "dataType": "Decimal", "value": "0.1" }
            ]
          },
          {
            "nodeType": "Operation",
            "operator": "If",
            "operands": [
              {
                "nodeType": "Operation",
                "operator": "GreaterThan",
                "operands": [
                  { "nodeType": "SourceValue", "sourcePath": "Amount" },
                  { "nodeType": "Constant", "dataType": "Decimal", "value": "1000" }
                ]
              },
              {
                "nodeType": "Operation",
                "operator": "Multiply",
                "operands": [
                  { "nodeType": "SourceValue", "sourcePath": "Amount" },
                  { "nodeType": "Constant", "dataType": "Decimal", "value": "0.05" }
                ]
              },
              { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
            ]
          }
        ]
      }
    ]
  },
  "isActive": true
}
```

## VIP zákazníci

### 4. VIP sleva
**Business požadavek**: 15% sleva pro VIP zákazníky

```json
{
  "name": "VIP sleva 15%",
  "description": "Speciální sleva pro VIP zákazníky",
  "targetEntityName": "Order",
  "targetProperty": "VipDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "Equal",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Customer.CustomerType" },
          { "nodeType": "Constant", "dataType": "String", "value": "VIP" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.15" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

### 5. Kombinovaná VIP sleva
**Business požadavek**: 20% sleva pro VIP zákazníky nad 1000 Kč nebo pro všechny nad 5000 Kč

```json
{
  "name": "Kombinovaná VIP sleva",
  "description": "20% sleva pro VIP nad 1000 Kč nebo pro všechny nad 5000 Kč",
  "targetEntityName": "Order",
  "targetProperty": "CombinedDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "Or",
        "operands": [
          {
            "nodeType": "Operation",
            "operator": "And",
            "operands": [
              {
                "nodeType": "Operation",
                "operator": "Equal",
                "operands": [
                  { "nodeType": "SourceValue", "sourcePath": "Customer.CustomerType" },
                  { "nodeType": "Constant", "dataType": "String", "value": "VIP" }
                ]
              },
              {
                "nodeType": "Operation",
                "operator": "GreaterThan",
                "operands": [
                  { "nodeType": "SourceValue", "sourcePath": "Amount" },
                  { "nodeType": "Constant", "dataType": "Decimal", "value": "1000" }
                ]
              }
            ]
          },
          {
            "nodeType": "Operation",
            "operator": "GreaterThan",
            "operands": [
              { "nodeType": "SourceValue", "sourcePath": "Amount" },
              { "nodeType": "Constant", "dataType": "Decimal", "value": "5000" }
            ]
          }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.2" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

## Množstevní slevy

### 6. Sleva podle počtu kusů
**Business požadavek**: 5% sleva při nákupu 10+ kusů

```json
{
  "name": "Množstevní sleva 5%",
  "description": "5% sleva při nákupu 10 a více kusů",
  "targetEntityName": "Order",
  "targetProperty": "QuantityDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThanOrEqual",
        "operands": [
          {
            "nodeType": "Aggregation",
            "aggregationType": "Sum",
            "collectionPath": "Items",
            "fieldPath": "Quantity"
          },
          { "nodeType": "Constant", "dataType": "Integer", "value": "10" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.05" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

### 7. Sleva podle počtu různých produktů
**Business požadavek**: 10% sleva při nákupu 5+ různých produktů

```json
{
  "name": "Sleva za rozmanitost",
  "description": "10% sleva při nákupu 5 a více různých produktů",
  "targetEntityName": "Order",
  "targetProperty": "DiversityDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThanOrEqual",
        "operands": [
          {
            "nodeType": "Aggregation",
            "aggregationType": "Count",
            "collectionPath": "Items"
          },
          { "nodeType": "Constant", "dataType": "Integer", "value": "5" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.1" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

## Doprava

### 8. Doprava zdarma
**Business požadavek**: Doprava zdarma pro objednávky nad 15 000 Kč

```json
{
  "name": "Doprava zdarma nad 15 000 Kč",
  "description": "Doprava zdarma pro velké objednávky",
  "targetEntityName": "Order",
  "targetProperty": "ShippingCost",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThanOrEqual",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "15000" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" },
      { "nodeType": "SourceValue", "sourcePath": "StandardShippingCost" }
    ]
  },
  "isActive": true
}
```

### 9. Expresní doprava zdarma pro VIP
**Business požadavek**: Expresní doprava zdarma pro VIP zákazníky nad 5000 Kč

```json
{
  "name": "Expresní doprava zdarma pro VIP",
  "description": "Expresní doprava zdarma pro VIP zákazníky nad 5000 Kč",
  "targetEntityName": "Order",
  "targetProperty": "ExpressShippingDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "And",
        "operands": [
          {
            "nodeType": "Operation",
            "operator": "Equal",
            "operands": [
              { "nodeType": "SourceValue", "sourcePath": "Customer.CustomerType" },
              { "nodeType": "Constant", "dataType": "String", "value": "VIP" }
            ]
          },
          {
            "nodeType": "Operation",
            "operator": "GreaterThan",
            "operands": [
              { "nodeType": "SourceValue", "sourcePath": "Amount" },
              { "nodeType": "Constant", "dataType": "Decimal", "value": "5000" }
            ]
          }
        ]
      },
      { "nodeType": "SourceValue", "sourcePath": "ExpressShippingCost" },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

## Věrnostní program

### 10. Věrnostní sleva podle celkových nákupů
**Business požadavek**: Sleva podle celkové hodnoty nákupů zákazníka za poslední rok

```json
{
  "name": "Věrnostní sleva podle celkových nákupů",
  "description": "Sleva podle celkové hodnoty nákupů zákazníka za poslední rok: nad 100 000 Kč = 20%, nad 50 000 Kč = 10%",
  "targetEntityName": "Order",
  "targetProperty": "LoyaltyDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThan",
        "operands": [
          {
            "nodeType": "Aggregation",
            "aggregationType": "Sum",
            "collectionPath": "Customer.Orders",
            "fieldPath": "Amount",
            "filter": {
              "nodeType": "Operation",
              "operator": "GreaterThan",
              "operands": [
                { "nodeType": "SourceValue", "sourcePath": "OrderDate" },
                { "nodeType": "Constant", "dataType": "DateTime", "value": "2024-01-01" }
              ]
            }
          },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "100000" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.2" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "If",
        "operands": [
          {
            "nodeType": "Operation",
            "operator": "GreaterThan",
            "operands": [
              {
                "nodeType": "Aggregation",
                "aggregationType": "Sum",
                "collectionPath": "Customer.Orders",
                "fieldPath": "Amount",
                "filter": {
                  "nodeType": "Operation",
                  "operator": "GreaterThan",
                  "operands": [
                    { "nodeType": "SourceValue", "sourcePath": "OrderDate" },
                    { "nodeType": "Constant", "dataType": "DateTime", "value": "2024-01-01" }
                  ]
                }
              },
              { "nodeType": "Constant", "dataType": "Decimal", "value": "50000" }
            ]
          },
          {
            "nodeType": "Operation",
            "operator": "Multiply",
            "operands": [
              { "nodeType": "SourceValue", "sourcePath": "Amount" },
              { "nodeType": "Constant", "dataType": "Decimal", "value": "0.1" }
            ]
          },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
        ]
      }
    ]
  },
  "isActive": true
}
```

## Faktury a platby

### 11. Upomínka po splatnosti
**Business požadavek**: Určuje, zda má být odeslána upomínka pro fakturu po splatnosti

```json
{
  "name": "Upomínka po splatnosti",
  "description": "Určuje, zda má být odeslána upomínka pro fakturu po splatnosti",
  "targetEntityName": "Invoice",
  "targetProperty": "RequiresReminder",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "And",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "Not",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "IsPaid" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "LessThan",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "DueDate" },
          { "nodeType": "Constant", "dataType": "DateTime", "value": "2024-12-01" }
        ]
      }
    ]
  },
  "isActive": true
}
```

### 12. Sleva za rychlou platbu
**Business požadavek**: 2% sleva při platbě do 7 dnů od vystavení faktury

```json
{
  "name": "Sleva za rychlou platbu",
  "description": "2% sleva při platbě do 7 dnů od vystavení faktury",
  "targetEntityName": "Invoice",
  "targetProperty": "EarlyPaymentDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "And",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "IsPaid" },
          {
            "nodeType": "Operation",
            "operator": "LessThanOrEqual",
            "operands": [
              {
                "nodeType": "Operation",
                "operator": "Subtract",
                "operands": [
                  { "nodeType": "SourceValue", "sourcePath": "PaymentDate" },
                  { "nodeType": "SourceValue", "sourcePath": "IssueDate" }
                ]
              },
              { "nodeType": "Constant", "dataType": "Integer", "value": "7" }
            ]
          }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "Amount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.02" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  },
  "isActive": true
}
```

## Testovací data pro příklady

### Pro objednávky (Order)
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "amount": 1500,
  "totalAmount": 1500,
  "orderDate": "2024-12-01T10:00:00Z",
  "customerId": "customer-123",
  "customer": {
    "customerType": "VIP",
    "orders": [
      { "amount": 2000, "orderDate": "2024-06-01T00:00:00Z" },
      { "amount": 3000, "orderDate": "2024-08-01T00:00:00Z" }
    ]
  },
  "items": [
    { "quantity": 2, "price": 500, "productId": "product-1" },
    { "quantity": 1, "price": 500, "productId": "product-2" }
  ],
  "standardShippingCost": 150,
  "expressShippingCost": 300
}
```

### Pro faktury (Invoice)
```json
{
  "id": "invoice-123",
  "amount": 2000,
  "issueDate": "2024-11-01T00:00:00Z",
  "dueDate": "2024-11-15T00:00:00Z",
  "paymentDate": "2024-11-05T00:00:00Z",
  "isPaid": true,
  "customerId": "customer-123"
}
```
