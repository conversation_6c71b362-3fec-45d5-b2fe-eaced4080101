using System.Linq.Expressions;

namespace Infrastructure.ExpressionEngine.Builders;

/// <summary>
/// Implementace builderu operačních výrazů.
/// Zodpovídá za vytváření Expression objektů pro aritmetické, logické a podmíněné operace.
/// </summary>
public class OperationExpressionBuilder : IOperationExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z operačního uzlu.
    /// </summary>
    /// <param name="node">Operační uzel s operátorem a operandy</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující operaci</returns>
    /// <exception cref="ArgumentNullException">Pokud je některý z parametrů null</exception>
    /// <exception cref="NotSupportedException">Pokud operátor nen<PERSON></exception>
    public System.Linq.Expressions.Expression BuildOperation(OperationNode node, ParameterExpression param, Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));
        if (buildChild == null) throw new ArgumentNullException(nameof(buildChild));

        var children = node.Operands.Select(n => buildChild(n, param)).ToList();
        
        return node.Operator switch
        {
            OperatorType.Add => System.Linq.Expressions.Expression.Add(children[0], children[1]),
            OperatorType.Subtract => System.Linq.Expressions.Expression.Subtract(children[0], children[1]),
            OperatorType.Multiply => System.Linq.Expressions.Expression.Multiply(children[0], children[1]),
            OperatorType.Divide => System.Linq.Expressions.Expression.Divide(children[0], children[1]),
            OperatorType.Equal => System.Linq.Expressions.Expression.Equal(children[0], children[1]),
            OperatorType.NotEqual => System.Linq.Expressions.Expression.NotEqual(children[0], children[1]),
            OperatorType.GreaterThan => System.Linq.Expressions.Expression.GreaterThan(children[0], children[1]),
            OperatorType.LessThan => System.Linq.Expressions.Expression.LessThan(children[0], children[1]),
            OperatorType.GreaterThanOrEqual => System.Linq.Expressions.Expression.GreaterThanOrEqual(children[0], children[1]),
            OperatorType.LessThanOrEqual => System.Linq.Expressions.Expression.LessThanOrEqual(children[0], children[1]),
            OperatorType.And => System.Linq.Expressions.Expression.AndAlso(AsBool(children[0]), AsBool(children[1])),
            OperatorType.Or => System.Linq.Expressions.Expression.OrElse(AsBool(children[0]), AsBool(children[1])),
            OperatorType.Not => System.Linq.Expressions.Expression.Not(AsBool(children[0])),
            OperatorType.If => System.Linq.Expressions.Expression.Condition(
                AsBool(children[0]),
                System.Linq.Expressions.Expression.Convert(children[1], children[2].Type),
                children[2]),
            _ => throw new NotSupportedException($"Operator not supported: {node.Operator}")
        };
    }

    /// <summary>
    /// Převede výraz na boolean typ pro logické operace.
    /// </summary>
    /// <param name="expression">Výraz k převodu</param>
    /// <returns>Boolean výraz</returns>
    private static System.Linq.Expressions.Expression AsBool(System.Linq.Expressions.Expression expression) =>
        expression.Type == typeof(bool) ? expression : System.Linq.Expressions.Expression.NotEqual(expression, System.Linq.Expressions.Expression.Constant(null, expression.Type));
}
