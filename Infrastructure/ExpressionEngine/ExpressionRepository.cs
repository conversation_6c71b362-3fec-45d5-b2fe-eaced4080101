using Application.Abstraction;
using Infrastructure.ExpressionEngine.API;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.ExpressionEngine;

/// <summary>
/// Implementace IExpressionRepository pro správu výrazů v databázi.
/// Poskytuje CRUD operace pro Expression entity s podporou cachování.
/// </summary>
public class ExpressionRepository : IExpressionRepository
{
    private readonly IApplicationDbContext _context;
    private readonly IExpressionCacheService _cacheService;

    /// <summary>
    /// Inicializuje novou instanci ExpressionRepository.
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    /// <param name="cacheService">Cache služba pro Expression Engine</param>
    public ExpressionRepository(IApplicationDbContext context, IExpressionCacheService cacheService)
    {
        _context = context;
        _cacheService = cacheService;
    }

    /// <summary>
    /// Získá všechny výrazy s cachováním.
    /// </summary>
    /// <returns>Kolekce všech výrazů</returns>
    public async Task<IEnumerable<Expression>> GetAllAsync()
    {
        const string cacheKey = "expressions:all";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<Expression>()
                .Where(e => e.IsActive)
                .OrderBy(e => e.Name)
                .ToListAsync();
        }, TimeSpan.FromMinutes(15));
    }

    /// <summary>
    /// Získá výraz podle ID s cachováním.
    /// </summary>
    /// <param name="id">ID výrazu</param>
    /// <returns>Nalezený výraz nebo null</returns>
    public async Task<Expression?> GetByIdAsync(Guid id)
    {
        var cacheKey = $"expressions:id:{id}";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<Expression>()
                .FirstOrDefaultAsync(e => e.Id == id);
        }, TimeSpan.FromMinutes(30));
    }

    /// <summary>
    /// Získá výraz podle názvu s cachováním.
    /// </summary>
    /// <param name="name">Název výrazu</param>
    /// <returns>Nalezený výraz nebo null</returns>
    public async Task<Expression?> GetByNameAsync(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return null;

        var cacheKey = $"expressions:name:{name}";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<Expression>()
                .FirstOrDefaultAsync(e => e.Name == name);
        }, TimeSpan.FromMinutes(30));
    }

    /// <summary>
    /// Přidá nový výraz a invaliduje související cache.
    /// </summary>
    /// <param name="expression">Výraz k přidání</param>
    public async Task AddAsync(Expression expression)
    {
        if (expression == null)
            throw new ArgumentNullException(nameof(expression));

        // Nastavíme ID pokud není nastaveno
        if (expression.Id == Guid.Empty)
            expression.Id = Guid.NewGuid();

        _context.Set<Expression>().Add(expression);
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache
        await InvalidateExpressionCacheAsync(expression);
    }

    /// <summary>
    /// Aktualizuje existující výraz a invaliduje související cache.
    /// </summary>
    /// <param name="expression">Výraz k aktualizaci</param>
    public async Task UpdateAsync(Expression expression)
    {
        if (expression == null)
            throw new ArgumentNullException(nameof(expression));

        var existingExpression = await _context.Set<Expression>()
            .FirstOrDefaultAsync(e => e.Id == expression.Id);

        if (existingExpression == null)
            throw new InvalidOperationException($"Výraz s ID {expression.Id} nebyl nalezen.");

        // Uložíme původní entity name pro invalidaci cache
        var originalEntityName = existingExpression.TargetEntityName;

        // Aktualizujeme vlastnosti
        existingExpression.Name = expression.Name;
        existingExpression.Description = expression.Description;
        existingExpression.TargetEntityName = expression.TargetEntityName;
        existingExpression.TargetProperty = expression.TargetProperty;
        existingExpression.SchemaVersion = expression.SchemaVersion;
        existingExpression.RootNode = expression.RootNode;
        existingExpression.IsActive = expression.IsActive;
        existingExpression.InternalNotes = expression.InternalNotes;

        // UpdatedAt se nastavuje automaticky v TrackableEntityInterceptor
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache pro původní i novou entitu
        await InvalidateExpressionCacheAsync(existingExpression);
        if (originalEntityName != existingExpression.TargetEntityName)
        {
            await _cacheService.InvalidateAsync($"expressions:entity:{originalEntityName}");
        }
    }

    /// <summary>
    /// Smaže výraz podle ID a invaliduje související cache.
    /// </summary>
    /// <param name="id">ID výrazu ke smazání</param>
    public async Task DeleteAsync(Guid id)
    {
        var expression = await _context.Set<Expression>()
            .FirstOrDefaultAsync(e => e.Id == id);

        if (expression == null)
            throw new InvalidOperationException($"Výraz s ID {id} nebyl nalezen.");

        _context.Set<Expression>().Remove(expression);
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache
        await InvalidateExpressionCacheAsync(expression);
    }

    /// <summary>
    /// Získá aktivní výrazy pro konkrétní entitu s cachováním.
    /// </summary>
    /// <param name="entityName">Název cílové entity</param>
    /// <returns>Kolekce aktivních výrazů pro entitu</returns>
    public async Task<IEnumerable<Expression>> GetActiveExpressionsForEntityAsync(string entityName)
    {
        if (string.IsNullOrWhiteSpace(entityName))
            throw new ArgumentException("Název entity nesmí být prázdný.", nameof(entityName));

        var cacheKey = $"expressions:entity:{entityName}";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<Expression>()
                .Where(e => e.IsActive && e.TargetEntityName == entityName)
                .OrderBy(e => e.Name)
                .ToListAsync();
        }, TimeSpan.FromMinutes(20));
    }

    /// <summary>
    /// Ověří, zda existuje výraz se zadaným názvem (pro validaci duplicit).
    /// </summary>
    /// <param name="name">Název výrazu</param>
    /// <param name="excludeId">ID výrazu k vyloučení z kontroly (pro update)</param>
    /// <returns>True pokud výraz s názvem existuje</returns>
    public async Task<bool> ExistsWithNameAsync(string name, Guid? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            return false;

        var query = _context.Set<Expression>().Where(e => e.Name == name);

        if (excludeId.HasValue)
            query = query.Where(e => e.Id != excludeId.Value);

        return await query.AnyAsync();
    }



    /// <summary>
    /// Invaliduje cache záznamy související s výrazem.
    /// </summary>
    /// <param name="expression">Výraz pro invalidaci cache</param>
    private async Task InvalidateExpressionCacheAsync(Expression expression)
    {
        // Invalidujeme všechny cache záznamy pro tento výraz
        await _cacheService.InvalidateAsync($"*:{expression.Id}:*");

        // Invalidujeme cache pro konkrétní entitu
        await _cacheService.InvalidateAsync($"expressions:entity:{expression.TargetEntityName}");

        // Invalidujeme obecné cache
        await _cacheService.InvalidateAsync("expressions:all");
    }
}
