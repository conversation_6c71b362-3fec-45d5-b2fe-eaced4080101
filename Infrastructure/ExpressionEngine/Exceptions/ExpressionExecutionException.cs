namespace Infrastructure.ExpressionEngine.Exceptions;

/// <summary>
/// Výjimka vyvolaná při chybě během vykonávání výrazu.
/// </summary>
public class ExpressionExecutionException : Exception
{
    /// <summary>
    /// Inicializuje novou instanci ExpressionExecutionException.
    /// </summary>
    /// <param name="message">Zpráva popisující chybu</param>
    public ExpressionExecutionException(string message) : base(message)
    {
    }

    /// <summary>
    /// Inicializuje novou instanci ExpressionExecutionException s vnitřní výjimkou.
    /// </summary>
    /// <param name="message">Zpráva popisující chybu</param>
    /// <param name="innerException">Vnitřní výjimka</param>
    public ExpressionExecutionException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
