using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace Infrastructure.Events.Persistence;

/// <summary>
/// Factory pro vytváření EventsDbContext během design-time operací (migrace, scaffolding).
/// </summary>
public class EventsDbContextFactory : IDesignTimeDbContextFactory<EventsDbContext>
{
    /// <summary>
    /// Vytvoří EventsDbContext pro design-time operace.
    /// </summary>
    /// <param name="args">Argumenty příkazové řádky</param>
    /// <returns>Nakonfigurovaný EventsDbContext</returns>
    public EventsDbContext CreateDbContext(string[] args)
    {
        // Načteme konfiguraci z appsettings.json
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .Build();

        // Získáme connection string
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        
        if (string.IsNullOrEmpty(connectionString))
        {
            // Fallback na výchozí connection string pro vývoj
            connectionString = "Data Source=./Data/datacapture.db";
        }

        // Vytvoříme DbContextOptions
        var optionsBuilder = new DbContextOptionsBuilder<EventsDbContext>();
        optionsBuilder.UseSqlite(connectionString);

        return new EventsDbContext(optionsBuilder.Options);
    }
}
