using Infrastructure.Events.Abstraction;
using Infrastructure.Events.Persistence.Configurations;
using Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Events.Persistence;

/// <summary>
/// Databázový kontext pro Events subsystém.
/// Separátní kontext umožňuje budoucí oddělení do samostatného NuGet balíčku.
/// </summary>
public class EventsDbContext : DbContext, IEventsDbContext
{
    /// <summary>
    /// Konstruktor pro EventsDbContext.
    /// </summary>
    /// <param name="options">Možnosti konfigurace kontextu</param>
    public EventsDbContext(DbContextOptions<EventsDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Kolekce definic událostí.
    /// </summary>
    public DbSet<EventDefinition> EventDefinitions { get; set; } = null!;

    /// <summary>
    /// Konfiguruje model databáze při jeho vytváření.
    /// </summary>
    /// <param name="modelBuilder">Builder pro konfiguraci modelu databáze</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Aplikujeme konfiguraci pro EventDefinition
        modelBuilder.ApplyConfiguration(new EventDefinitionConfiguration());

        // Konfigurace pro SharedKernel entity
        ConfigureSharedKernelEntities(modelBuilder);
    }

    /// <summary>
    /// Konfiguruje obecné vlastnosti pro všechny entity dědící ze SharedKernel base tříd.
    /// </summary>
    /// <param name="modelBuilder">Builder pro konfiguraci modelu databáze</param>
    private void ConfigureSharedKernelEntities(ModelBuilder modelBuilder)
    {
        // Najdeme všechny entity typy v modelu
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var clrType = entityType.ClrType;

            // Zkontrolujeme, zda entita implementuje IEntity<T> ze SharedKernel
            var entityInterface = clrType.GetInterfaces()
                .FirstOrDefault(i => i.IsGenericType &&
                               i.GetGenericTypeDefinition() == typeof(SharedKernel.Domain.IEntity<>));

            if (entityInterface != null)
            {
                // Konfigurace RowVersion pro optimistické zamykání
                var rowVersionProperty = entityType.FindProperty("RowVersion");
                if (rowVersionProperty != null)
                {
                    modelBuilder.Entity(clrType)
                        .Property("RowVersion")
                        .IsRowVersion()
                        .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 })
                        .HasComment("Verze řádku pro optimistické zamykání");
                }
            }

            // Zkontrolujeme, zda entita dědí z BaseEntity<T> a má DomainEvents
            if (clrType.IsAssignableFromGeneric(typeof(SharedKernel.Domain.BaseEntity<>)))
            {
                // Vyloučíme DomainEvents z mapování do databáze
                modelBuilder.Entity(clrType)
                    .Ignore("DomainEvents");
            }
        }
    }
}
