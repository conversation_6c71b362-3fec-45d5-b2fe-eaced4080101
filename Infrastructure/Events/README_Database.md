# EventDefinition - Zjednodušený databázový subsystém

Tento dokument popisuje zjednodušený EventDefinition subsystém, který kombinuje databázovou perzistenci s rychlým přístupem přes cache pro interceptor.

## Přehled

Zjednodušený EventDefinition subsystém poskytuje:
- **Jeden zdroj pravdy** - databáze jako jedin<PERSON> definic událostí
- **Rychlý přístup** - cache pro vysoký výkon interceptoru
- **Separátní databázový kontext** (`EventsDbContext`) pro budoucí oddělení do NuGet balíčku
- **CRUD operace** pro správu definic událostí
- **Automatické obnovení cache** při změnách v databázi

## Zjednodušení

Původní systém měl duplicitní funkcionalitu:
- ❌ **EventDefinitionRegistry** - in-memory registry
- ❌ **EventDefinitionRegistrationService** - hosted service pro registraci
- ❌ **IEventDefinitionRegistry** - rozhraní pro registry

Nový zjednodušený systém:
- ✅ **EventDefinitionService** - CRUD operace s databází
- ✅ **EventDefinitionProvider** - cache provider pro interceptor
- ✅ **IEventDefinitionProvider** - jednoduché rozhraní pro získání definic

## Architektura

### Databázový kontext

```csharp
// Separátní kontext pro Events subsystém
public class EventsDbContext : DbContext, IEventsDbContext
{
    public DbSet<EventDefinition> EventDefinitions { get; set; }
}
```

### Služby

```csharp
// Služba pro CRUD operace s databází
public class EventDefinitionService
{
    Task<List<EventDefinition>> GetAllAsync();
    Task<EventDefinition?> GetByIdAsync(int id);
    Task<EventDefinition> CreateAsync(EventDefinition eventDefinition);
    Task<EventDefinition> UpdateAsync(EventDefinition eventDefinition);
    Task<bool> DeleteAsync(int id);
    // + automatické obnovení cache při změnách
}

// Provider s cache pro interceptor
public class EventDefinitionProvider : IEventDefinitionProvider
{
    Task<IEnumerable<EventDefinition>> GetDefinitionsAsync(Type entityType, EventType operation);
    Task<IEnumerable<EventDefinition>> GetDefinitionsAsync(Type entityType);
    Task RefreshCacheAsync();
    // + automatické expirování cache (5 minut)
}
```

## Databázové mapování

EventDefinition třída byla rozšířena o vlastnosti kompatibilní s databází:

### Původní vlastnosti (runtime)
```csharp
public Type EntityType { get; set; }                    // Runtime typ entity
public List<string> TrackedProperties { get; set; }     // Runtime seznam vlastností
public Func<object, bool>? Condition { get; set; }      // Runtime podmínka
```

### Databázové vlastnosti (persistence)
```csharp
public string EntityTypeName { get; set; }              // Název typu pro DB
public string TrackedPropertiesJson { get; set; }       // JSON seznam vlastností
public string? ConditionExpression { get; set; }        // String výraz podmínky
```

### Automatická konverze

Třída automaticky konvertuje mezi runtime a databázovými vlastnostmi:

```csharp
// Nastavení typu entity automaticky aktualizuje EntityTypeName
eventDefinition.EntityType = typeof(SampleEntity);
// EntityTypeName = "Domain.Entities.SampleEntity, Domain, Version=..."

// Nastavení sledovaných vlastností automaticky aktualizuje JSON
eventDefinition.TrackedProperties = new List<string> { "Name", "Age" };
// TrackedPropertiesJson = "[\"Name\",\"Age\"]"
```

## Konfigurace databáze

### Connection String

Events subsystém používá stejný connection string jako hlavní aplikace:

```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=../Data/datacapture.db"
  }
}
```

### Registrace v DI

```csharp
// Infrastructure/DependencyInjection.cs
services.AddDbContext<EventsDbContext>(options =>
{
    options.UseSqlite(connectionString);
});
services.AddScoped<IEventsDbContext>(provider =>
    provider.GetRequiredService<EventsDbContext>());

// Registrace služeb pro Events subsystém
services.AddScoped<EventDefinitionService>();
services.AddScoped<IEventDefinitionProvider, EventDefinitionProvider>();
```

## Použití

### 1. Vytvoření definice události

```csharp
var eventDefinitionService = serviceProvider.GetRequiredService<EventDefinitionService>();

var definition = new EventDefinition(
    "SampleEntityCreated", 
    typeof(SampleEntity), 
    EventType.Created)
{
    Description = "Událost při vytvoření ukázkové entity"
};

await eventDefinitionService.CreateAsync(definition);
```

### 2. Načtení definic z databáze

```csharp
// Všechny definice
var allDefinitions = await eventDefinitionService.GetAllAsync();

// Pouze aktivní definice
var activeDefinitions = await eventDefinitionService.GetActiveAsync();

// Definice pro konkrétní typ entity
var entityTypeName = typeof(SampleEntity).AssemblyQualifiedName;
var entityDefinitions = await eventDefinitionService.GetByEntityTypeAsync(entityTypeName);

// Definice podle názvu
var definition = await eventDefinitionService.GetByNameAsync("SampleEntityCreated");
```

### 3. Aktualizace definice

```csharp
var definition = await eventDefinitionService.GetByNameAsync("SampleEntityCreated");
if (definition != null)
{
    definition.Description = "Aktualizovaný popis události";
    definition.TrackedProperties = new List<string> { "Name", "Description" };
    
    await eventDefinitionService.UpdateAsync(definition);
}
```

### 4. Deaktivace/aktivace definice

```csharp
// Deaktivace
await eventDefinitionService.DeactivateAsync(definitionId);

// Aktivace
await eventDefinitionService.ActivateAsync(definitionId);
```

## Migrace

Databázová tabulka `EventDefinitions` byla vytvořena pomocí EF Core migrací:

```bash
# Vytvoření migrace
dotnet ef migrations add AddEventDefinitions --context EventsDbContext --project Infrastructure --startup-project API

# Aplikace migrace
dotnet ef database update --context EventsDbContext --project Infrastructure --startup-project API
```

### Struktura tabulky

```sql
CREATE TABLE "EventDefinitions" (
    "Id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "EventName" TEXT NOT NULL,                    -- Jedinečný název události
    "EntityTypeName" TEXT NOT NULL,               -- Plný název typu entity
    "Operation" TEXT NOT NULL,                    -- Typ operace (Created/Updated/Deleted/Any)
    "TrackedPropertiesJson" TEXT NOT NULL DEFAULT '[]', -- JSON seznam vlastností
    "ConditionExpression" TEXT NULL,              -- Podmínka jako string výraz
    "Description" TEXT NULL,                      -- Popis události
    "IsActive" INTEGER NOT NULL DEFAULT 1,       -- Aktivní stav
    "CreatedAt" TEXT NOT NULL,                    -- Datum vytvoření
    "RowVersion" BLOB NOT NULL DEFAULT X'0000000000000001' -- Optimistické zamykání
);
```

### Indexy

- `IX_EventDefinitions_EventName_Unique` - Jedinečný index na EventName
- `IX_EventDefinitions_EntityTypeName` - Index na EntityTypeName
- `IX_EventDefinitions_EntityTypeName_Operation` - Kompozitní index
- `IX_EventDefinitions_IsActive` - Index na IsActive

## Výhody zjednodušeného systému

1. **Jeden zdroj pravdy** - Databáze jako jediné úložiště definic
2. **Rychlý přístup** - Cache pro vysoký výkon interceptoru
3. **Automatická synchronizace** - Cache se obnovuje při změnách v databázi
4. **Jednodušší architektura** - Méně komponent, méně složitosti
5. **Modulárnost** - Events subsystém může být snadno oddělen do NuGet balíčku
6. **Testovatelnost** - Snadné mockování pro unit testy

## Tok dat

```
Uživatel → EventDefinitionService → Databáze
                    ↓
            Automatické obnovení cache
                    ↓
         EventDefinitionProvider ← DomainEventInterceptor
```

## Budoucí rozšíření

- **API endpointy** pro správu definic událostí
- **UI rozhraní** pro vizuální správu definic
- **Import/Export** definic událostí
- **Validace podmínek** při vytváření definic
- **Audit log** změn definic událostí

## Příklady použití

Kompletní příklady použití najdete v souboru `Infrastructure/Events/Examples/DatabaseEventDefinitionsExample.cs`.
