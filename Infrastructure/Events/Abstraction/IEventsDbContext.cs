using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;

namespace Infrastructure.Events.Abstraction;

/// <summary>
/// Rozhraní pro databázový kontext Events subsystému.
/// Poskytuje abstrakci pro přístup k databázi událostí.
/// </summary>
public interface IEventsDbContext
{
    /// <summary>
    /// Kolekce definic událostí.
    /// </summary>
    DbSet<EventDefinition> EventDefinitions { get; set; }

    /// <summary>
    /// Získá DbSet pro daný typ entity.
    /// </summary>
    /// <typeparam name="T">Typ entity</typeparam>
    /// <returns>DbSet pro daný typ</returns>
    DbSet<T> Set<T>() where T : class;

    /// <summary>
    /// Uloží změny do databáze asynchronně.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet záznamů, kter<PERSON> byly uloženy</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Poskytuje přístup k databázovým operacím.
    /// </summary>
    DatabaseFacade Database { get; }
}
