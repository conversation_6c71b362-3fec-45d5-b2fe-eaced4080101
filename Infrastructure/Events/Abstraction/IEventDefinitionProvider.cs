using SharedKernel.Domain;

namespace Infrastructure.Events.Abstraction;

/// <summary>
/// Rozhraní pro poskytování definic událostí.
/// Zjednodušené rozhraní pro získání aktivních definic událostí z databáze s cache.
/// </summary>
public interface IEventDefinitionProvider
{
    /// <summary>
    /// Získá všechny aktivní definice událostí pro daný typ entity a operaci.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Kolekce definic událostí</returns>
    Task<IEnumerable<EventDefinition>> GetDefinitionsAsync(Type entityType, EventType operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Získá všechny aktivní definice událostí pro daný typ entity.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Kolekce definic událostí</returns>
    Task<IEnumerable<EventDefinition>> GetDefinitionsAsync(Type entityType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obnoví cache definic událostí z databáze.
    /// Volá se automaticky při změnách v databázi.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    Task RefreshCacheAsync(CancellationToken cancellationToken = default);
}
