using Domain.Entities;
using Infrastructure.Events.Services;
using SharedKernel.Domain;

namespace Infrastructure.Events.Examples;

/// <summary>
/// Ukázkový kód demonstrují<PERSON><PERSON> práci s EventDefinition v databázi.
/// Ukazuje, jak vytvářet, ukládat a načítat definice událostí za chodu aplikace.
/// </summary>
public static class DatabaseEventDefinitionsExample
{
    /// <summary>
    /// Vytvoří uk<PERSON>zkové definice událostí a uloží je do databáze.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    public static async Task CreateSampleDefinitionsAsync(
        EventDefinitionService eventDefinitionService, 
        CancellationToken cancellationToken = default)
    {
        // Základní definice události při vytvoření entity
        var createdDefinition = new EventDefinition(
            "SampleEntityCreated", 
            typeof(SampleEntity), 
            EventType.Created)
        {
            Description = "Událost spuštěná při vytvoření nové ukázkové entity"
        };

        await eventDefinitionService.CreateAsync(createdDefinition, cancellationToken);

        // Definice události při změně názvu
        var nameChangedDefinition = new EventDefinition(
            "SampleEntityNameChanged", 
            typeof(SampleEntity), 
            EventType.Updated)
        {
            Description = "Událost spuštěná při změně názvu ukázkové entity"
        };
        nameChangedDefinition.TrackedProperties = new List<string> { nameof(SampleEntity.Name) };

        await eventDefinitionService.CreateAsync(nameChangedDefinition, cancellationToken);

        // Definice události při aktivaci entity s podmínkou
        var activatedDefinition = new EventDefinition(
            "SampleEntityActivated",
            typeof(SampleEntity),
            EventType.Updated)
        {
            Description = "Událost spuštěná při aktivaci ukázkové entity",
            ConditionExpressionName = "SampleEntityIsActiveCheck"
        };
        activatedDefinition.TrackedProperties = new List<string> { nameof(SampleEntity.IsActive) };

        await eventDefinitionService.CreateAsync(activatedDefinition, cancellationToken);

        // Definice události při smazání
        var deletedDefinition = new EventDefinition(
            "SampleEntityDeleted", 
            typeof(SampleEntity), 
            EventType.Deleted)
        {
            Description = "Událost spuštěná při smazání ukázkové entity"
        };

        await eventDefinitionService.CreateAsync(deletedDefinition, cancellationToken);
    }

    /// <summary>
    /// Načte a zobrazí všechny definice událostí z databáze.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    public static async Task DisplayAllDefinitionsAsync(
        EventDefinitionService eventDefinitionService, 
        CancellationToken cancellationToken = default)
    {
        var definitions = await eventDefinitionService.GetAllAsync(cancellationToken);

        Console.WriteLine($"Nalezeno {definitions.Count} definic událostí:");
        Console.WriteLine();

        foreach (var definition in definitions)
        {
            Console.WriteLine($"ID: {definition.Id}");
            Console.WriteLine($"Název: {definition.EventName}");
            Console.WriteLine($"Typ entity: {definition.EntityName}");
            Console.WriteLine($"Operace: {definition.Operation}");
            Console.WriteLine($"Aktivní: {(definition.IsActive ? "Ano" : "Ne")}");
            Console.WriteLine($"Sledované vlastnosti: {string.Join(", ", definition.TrackedProperties)}");
            
            if (!string.IsNullOrEmpty(definition.ConditionExpressionName))
                Console.WriteLine($"Podmínka: {definition.ConditionExpressionName}");
            
            if (!string.IsNullOrEmpty(definition.Description))
                Console.WriteLine($"Popis: {definition.Description}");
            
            Console.WriteLine($"Vytvořeno: {definition.CreatedAt:dd.MM.yyyy HH:mm:ss}");
            Console.WriteLine(new string('-', 50));
        }
    }

    /// <summary>
    /// Načte definice událostí pro konkrétní typ entity.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="entityType">Typ entity</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    public static async Task DisplayDefinitionsForEntityAsync(
        EventDefinitionService eventDefinitionService, 
        Type entityType,
        CancellationToken cancellationToken = default)
    {
        var entityTypeName = entityType.AssemblyQualifiedName ?? entityType.FullName ?? entityType.Name;
        var definitions = await eventDefinitionService.GetByEntityTypeAsync(entityTypeName, cancellationToken);

        Console.WriteLine($"Definice událostí pro entitu {entityType.Name}:");
        Console.WriteLine();

        foreach (var definition in definitions)
        {
            Console.WriteLine($"- {definition.EventName} ({definition.Operation})");
            if (definition.TrackedProperties.Any())
                Console.WriteLine($"  Sledované vlastnosti: {string.Join(", ", definition.TrackedProperties)}");
            if (!string.IsNullOrEmpty(definition.Description))
                Console.WriteLine($"  Popis: {definition.Description}");
        }
    }

    /// <summary>
    /// Aktualizuje existující definici události.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="eventName">Název události k aktualizaci</param>
    /// <param name="newDescription">Nový popis</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    public static async Task UpdateDefinitionAsync(
        EventDefinitionService eventDefinitionService,
        string eventName,
        string newDescription,
        CancellationToken cancellationToken = default)
    {
        var definition = await eventDefinitionService.GetByNameAsync(eventName, cancellationToken);
        if (definition == null)
        {
            Console.WriteLine($"Definice události '{eventName}' nebyla nalezena.");
            return;
        }

        definition.Description = newDescription;
        await eventDefinitionService.UpdateAsync(definition, cancellationToken);
        
        Console.WriteLine($"Definice události '{eventName}' byla aktualizována.");
    }

    /// <summary>
    /// Deaktivuje definici události.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="eventName">Název události k deaktivaci</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    public static async Task DeactivateDefinitionAsync(
        EventDefinitionService eventDefinitionService,
        string eventName,
        CancellationToken cancellationToken = default)
    {
        var definition = await eventDefinitionService.GetByNameAsync(eventName, cancellationToken);
        if (definition == null)
        {
            Console.WriteLine($"Definice události '{eventName}' nebyla nalezena.");
            return;
        }

        var success = await eventDefinitionService.DeactivateAsync(definition.Id, cancellationToken);
        if (success)
            Console.WriteLine($"Definice události '{eventName}' byla deaktivována.");
        else
            Console.WriteLine($"Nepodařilo se deaktivovat definici události '{eventName}'.");
    }
}
