using Infrastructure.Events.Abstraction;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SharedKernel.Domain;

namespace Infrastructure.Events.Services;

/// <summary>
/// Služba pro správu definic událostí v databázi.
/// Poskytuje CRUD operace pro EventDefinition entity.
/// </summary>
public class EventDefinitionService
{
    private readonly IEventsDbContext _context;
    private readonly ILogger<EventDefinitionService> _logger;
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// Konstruktor služby.
    /// </summary>
    /// <param name="context">Databázový kontext pro Events subsystém</param>
    /// <param name="logger">Logger</param>
    /// <param name="serviceProvider">Service provider pro lazy loading provideru</param>
    public EventDefinitionService(IEventsDbContext context, ILogger<EventDefinitionService> logger, IServiceProvider serviceProvider)
    {
        _context = context;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// Získá všechny definice událostí z databáze.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Seznam všech definic událostí</returns>
    public async Task<List<EventDefinition>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.EventDefinitions
            .OrderBy(e => e.EventName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Získá aktivní definice událostí z databáze.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Seznam aktivních definic událostí</returns>
    public async Task<List<EventDefinition>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        return await _context.EventDefinitions
            .Where(e => e.IsActive)
            .OrderBy(e => e.EventName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Získá definici události podle ID.
    /// </summary>
    /// <param name="id">ID definice události</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Definice události nebo null</returns>
    public async Task<EventDefinition?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.EventDefinitions
            .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
    }

    /// <summary>
    /// Získá definici události podle názvu.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Definice události nebo null</returns>
    public async Task<EventDefinition?> GetByNameAsync(string eventName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(eventName))
            return null;

        return await _context.EventDefinitions
            .FirstOrDefaultAsync(e => e.EventName == eventName, cancellationToken);
    }

    /// <summary>
    /// Získá definice událostí pro daný typ entity.
    /// </summary>
    /// <param name="entityTypeName">Název typu entity</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Seznam definic událostí pro daný typ entity</returns>
    public async Task<List<EventDefinition>> GetByEntityTypeAsync(string entityTypeName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(entityTypeName))
            return new List<EventDefinition>();

        return await _context.EventDefinitions
            .Where(e => e.EntityTypeName == entityTypeName && e.IsActive)
            .OrderBy(e => e.EventName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Získá definice událostí pro daný typ entity a operaci.
    /// </summary>
    /// <param name="entityTypeName">Název typu entity</param>
    /// <param name="operation">Typ operace</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Seznam definic událostí</returns>
    public async Task<List<EventDefinition>> GetByEntityTypeAndOperationAsync(
        string entityTypeName, 
        EventType operation, 
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(entityTypeName))
            return new List<EventDefinition>();

        return await _context.EventDefinitions
            .Where(e => e.EntityTypeName == entityTypeName && 
                       e.IsActive && 
                       (e.Operation == operation || e.Operation == EventType.Any))
            .OrderBy(e => e.EventName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Vytvoří novou definici události.
    /// </summary>
    /// <param name="eventDefinition">Definice události k vytvoření</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Vytvořená definice události</returns>
    public async Task<EventDefinition> CreateAsync(EventDefinition eventDefinition, CancellationToken cancellationToken = default)
    {
        if (eventDefinition == null)
            throw new ArgumentNullException(nameof(eventDefinition));

        // Ověříme, že název události je jedinečný
        var existingDefinition = await GetByNameAsync(eventDefinition.EventName, cancellationToken);
        if (existingDefinition != null)
            throw new InvalidOperationException($"Definice události s názvem '{eventDefinition.EventName}' již existuje.");

        eventDefinition.CreatedAt = DateTimeOffset.UtcNow;
        
        _context.EventDefinitions.Add(eventDefinition);
        await _context.SaveChangesAsync(cancellationToken);

        // Obnovíme cache po změně
        await RefreshCacheAsync(cancellationToken);

        return eventDefinition;
    }

    /// <summary>
    /// Aktualizuje existující definici události.
    /// </summary>
    /// <param name="eventDefinition">Definice události k aktualizaci</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Aktualizovaná definice události</returns>
    public async Task<EventDefinition> UpdateAsync(EventDefinition eventDefinition, CancellationToken cancellationToken = default)
    {
        if (eventDefinition == null)
            throw new ArgumentNullException(nameof(eventDefinition));

        // Ověříme, že definice existuje
        var existingDefinition = await GetByIdAsync(eventDefinition.Id, cancellationToken);
        if (existingDefinition == null)
            throw new InvalidOperationException($"Definice události s ID {eventDefinition.Id} neexistuje.");

        // Ověříme, že název události je jedinečný (kromě aktuální definice)
        var duplicateDefinition = await _context.EventDefinitions
            .FirstOrDefaultAsync(e => e.EventName == eventDefinition.EventName && e.Id != eventDefinition.Id, cancellationToken);
        if (duplicateDefinition != null)
            throw new InvalidOperationException($"Definice události s názvem '{eventDefinition.EventName}' již existuje.");

        _context.EventDefinitions.Update(eventDefinition);
        await _context.SaveChangesAsync(cancellationToken);

        // Obnovíme cache po změně
        await RefreshCacheAsync(cancellationToken);

        return eventDefinition;
    }

    /// <summary>
    /// Smaže definici události.
    /// </summary>
    /// <param name="id">ID definice události k smazání</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>True pokud byla definice smazána, false pokud neexistovala</returns>
    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var eventDefinition = await GetByIdAsync(id, cancellationToken);
        if (eventDefinition == null)
            return false;

        _context.EventDefinitions.Remove(eventDefinition);
        await _context.SaveChangesAsync(cancellationToken);

        // Obnovíme cache po změně
        await RefreshCacheAsync(cancellationToken);

        return true;
    }

    /// <summary>
    /// Aktivuje definici události.
    /// </summary>
    /// <param name="id">ID definice události</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>True pokud byla definice aktivována, false pokud neexistovala</returns>
    public async Task<bool> ActivateAsync(int id, CancellationToken cancellationToken = default)
    {
        var eventDefinition = await GetByIdAsync(id, cancellationToken);
        if (eventDefinition == null)
            return false;

        eventDefinition.IsActive = true;
        await _context.SaveChangesAsync(cancellationToken);

        // Obnovíme cache po změně
        await RefreshCacheAsync(cancellationToken);

        return true;
    }

    /// <summary>
    /// Deaktivuje definici události.
    /// </summary>
    /// <param name="id">ID definice události</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>True pokud byla definice deaktivována, false pokud neexistovala</returns>
    public async Task<bool> DeactivateAsync(int id, CancellationToken cancellationToken = default)
    {
        var eventDefinition = await GetByIdAsync(id, cancellationToken);
        if (eventDefinition == null)
            return false;

        eventDefinition.IsActive = false;
        await _context.SaveChangesAsync(cancellationToken);

        // Obnovíme cache po změně
        await RefreshCacheAsync(cancellationToken);

        return true;
    }

    /// <summary>
    /// Obnoví cache definic událostí pokud je provider dostupný.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    private async Task RefreshCacheAsync(CancellationToken cancellationToken)
    {
        try
        {
            var provider = _serviceProvider.GetService<IEventDefinitionProvider>();
            if (provider != null)
            {
                await provider.RefreshCacheAsync(cancellationToken);
                _logger.LogDebug("Cache definic událostí byla obnovena po změně v databázi.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Nepodařilo se obnovit cache definic událostí po změně v databázi.");
        }
    }
}
