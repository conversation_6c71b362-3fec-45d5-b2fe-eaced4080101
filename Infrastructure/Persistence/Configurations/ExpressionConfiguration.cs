using Infrastructure.ExpressionEngine;
using Infrastructure.Conversions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;
using Infrastructure.Persistence.Conversions;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurace Entity Framework pro Expression entitu.
/// Definuje mapování na databázovou tabulku a serializaci ExpressionNode jako JSON.
/// </summary>
public class ExpressionConfiguration : IEntityTypeConfiguration<Expression>
{
    /// <summary>
    /// Konfiguruje mapování Expression entity.
    /// </summary>
    /// <param name="builder">Builder pro konfiguraci entity</param>
    public void Configure(EntityTypeBuilder<Expression> builder)
    {
        // Název tabulky
        builder.ToTable("Expressions");

        // Primary key
        builder.HasKey(e => e.Id);

        // Vlastnosti
        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Název výrazu");

        builder.Property(e => e.Description)
            .HasMaxLength(1000)
            .HasComment("Popis účelu a použití výrazu");

        builder.Property(e => e.TargetEntityName)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("Název cílové entity pro aplikaci výrazu");

        builder.Property(e => e.TargetProperty)
            .HasMaxLength(100)
            .HasComment("Název vlastnosti cílové entity pro výsledek výrazu");

        builder.Property(e => e.SchemaVersion)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("1.0")
            .HasComment("Verze schématu výrazu pro kompatibilitu");

        builder.Property(e => e.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("Určuje, zda je výraz aktivní");

        builder.Property(e => e.InternalNotes)
            .HasMaxLength(2000)
            .HasComment("Interní poznámky pro vývojáře");

        // Vlastnosti pro verzování
        builder.Property(e => e.Version)
            .IsRequired()
            .HasDefaultValue(1)
            .HasComment("Číslo verze výrazu pro podporu verzování");

        builder.Property(e => e.EffectiveFrom)
            .IsRequired()
            .HasComment("Datum a čas, od kterého je tato verze výrazu platná");

        builder.Property(e => e.EffectiveTo)
            .HasComment("Datum a čas, do kterého je tato verze výrazu platná");

        // RootNode jako JSON s podporou polymorfních typů
        builder.Property(e => e.RootNode)
            .IsRequired()
            .HasConversion(
                v => JsonSerializer.Serialize(v, DefaultJsonSerializerOptions.Options),
                v => JsonSerializer.Deserialize<ExpressionNode>(v, DefaultJsonSerializerOptions.Options)!)
            .HasColumnType("TEXT")
            .HasComment("Kořenový uzel výrazu serializovaný jako JSON");

        // Indexy pro výkon
        builder.HasIndex(e => e.Name)
            .HasDatabaseName("IX_Expressions_Name");

        builder.HasIndex(e => e.TargetEntityName)
            .HasDatabaseName("IX_Expressions_TargetEntityName");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Expressions_IsActive");

        builder.HasIndex(e => new { e.TargetEntityName, e.IsActive })
            .HasDatabaseName("IX_Expressions_TargetEntity_Active");

        // Indexy pro verzování
        builder.HasIndex(e => e.Version)
            .HasDatabaseName("IX_Expressions_Version");

        builder.HasIndex(e => e.EffectiveFrom)
            .HasDatabaseName("IX_Expressions_EffectiveFrom");

        builder.HasIndex(e => e.EffectiveTo)
            .HasDatabaseName("IX_Expressions_EffectiveTo");

        builder.HasIndex(e => new { e.Name, e.Version })
            .IsUnique()
            .HasDatabaseName("IX_Expressions_Name_Version");

        // RowVersion pro optimistické zamykání
        builder.Property(e => e.RowVersion)
            .IsRowVersion()
            .HasComment("Verze řádku pro optimistické zamykání");
    }
}
