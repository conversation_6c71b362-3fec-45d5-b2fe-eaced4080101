using Domain.Events;
using Infrastructure.Events.Abstraction;
using Infrastructure.Events.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using SharedKernel.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using DomainEventDefinition = Infrastructure.Events.EventDefinition;

namespace Infrastructure.Persistence.Interceptors;

/// <summary>
/// Interceptor pro automatické generování doménových událostí na základě definic událostí.
/// Spolupracuje s EventDefinitionProvider pro určení, které události se mají generovat.
/// </summary>
public class DomainEventInterceptor : SaveChangesInterceptor
{
    private readonly IEventDefinitionProvider _eventDefinitionProvider;
    private readonly EventConditionEvaluator _conditionEvaluator;

    /// <summary>
    /// Inicializuje novou instanci DomainEventInterceptor.
    /// </summary>
    /// <param name="eventDefinitionProvider">Provider definic událostí</param>
    /// <param name="conditionEvaluator">Evaluátor podmínek událostí</param>
    public DomainEventInterceptor(
        IEventDefinitionProvider eventDefinitionProvider,
        EventConditionEvaluator conditionEvaluator)
    {
        _eventDefinitionProvider = eventDefinitionProvider ?? throw new ArgumentNullException(nameof(eventDefinitionProvider));
        _conditionEvaluator = conditionEvaluator ?? throw new ArgumentNullException(nameof(conditionEvaluator));
    }

    /// <summary>
    /// Asynchronní zpracování před uložením změn - generuje doménové události.
    /// </summary>
    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        if (context == null)
            return result;

        await GenerateDomainEventsAsync(context);
        return result;
    }

    /// <summary>
    /// Synchronní zpracování před uložením změn - generuje doménové události.
    /// </summary>
    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result)
    {
        var context = eventData.Context;
        if (context == null)
            return result;

        GenerateDomainEvents(context);
        return result;
    }

    /// <summary>
    /// Generuje doménové události pro změněné entity (asynchronní verze).
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    private async Task GenerateDomainEventsAsync(DbContext context, CancellationToken cancellationToken = default)
    {
        var entries = context.ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added ||
                       e.State == EntityState.Modified ||
                       e.State == EntityState.Deleted)
            .ToList();

        foreach (var entry in entries)
        {
            var operation = GetEntityOperation(entry.State);
            var definitions = await _eventDefinitionProvider.GetDefinitionsAsync(entry.Entity.GetType(), operation, cancellationToken);

            foreach (var definition in definitions)
            {
                if (await ShouldGenerateEventAsync(entry, definition, cancellationToken))
                {
                    var domainEvent = CreateDomainEvent(entry, definition);
                    if (domainEvent != null)
                    {
                        AddDomainEventToEntity(entry.Entity, domainEvent);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Generuje doménové události pro změněné entity (synchronní verze).
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    private void GenerateDomainEvents(DbContext context)
    {
        // Pro synchronní verzi použijeme GetAwaiter().GetResult()
        GenerateDomainEventsAsync(context, CancellationToken.None).GetAwaiter().GetResult();
    }

    /// <summary>
    /// Převede EntityState na EventType.
    /// </summary>
    /// <param name="state">Stav entity</param>
    /// <returns>Typ události</returns>
    private static EventType GetEntityOperation(EntityState state)
    {
        return state switch
        {
            EntityState.Added => EventType.Created,
            EntityState.Modified => EventType.Updated,
            EntityState.Deleted => EventType.Deleted,
            _ => throw new ArgumentException($"Nepodporovaný stav entity: {state}")
        };
    }

    /// <summary>
    /// Ověří, zda se má pro danou entitu a definici generovat událost.
    /// </summary>
    /// <param name="entry">Entry entity</param>
    /// <param name="definition">Definice události</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>True, pokud se má událost generovat</returns>
    private async Task<bool> ShouldGenerateEventAsync(EntityEntry entry, DomainEventDefinition definition, CancellationToken cancellationToken)
    {
        // Ověření podmínky definice pomocí Expression Engine
        var conditionResult = await _conditionEvaluator.EvaluateConditionAsync(
            definition.ConditionExpressionName,
            entry.Entity,
            cancellationToken);

        // Pokud je podmínka definována a není splněna, událost se negeneruje
        if (conditionResult.HasValue && !conditionResult.Value)
            return false;

        // Pro Update operace ověříme, zda se změnily sledované vlastnosti
        if (entry.State == EntityState.Modified && definition.TrackedProperties.Any())
        {
            var changedProperties = GetChangedProperties(entry);
            return HasTrackedPropertyChanged(definition.TrackedProperties, changedProperties);
        }

        return true;
    }

    /// <summary>
    /// Ověří, zda se změnila některá ze sledovaných vlastností.
    /// </summary>
    /// <param name="trackedProperties">Seznam sledovaných vlastností</param>
    /// <param name="changedProperties">Seznam změněných vlastností</param>
    /// <returns>True pokud se změnila některá sledovaná vlastnost</returns>
    private static bool HasTrackedPropertyChanged(IEnumerable<string> trackedProperties, IEnumerable<string> changedProperties)
    {
        return trackedProperties.Any(tracked => changedProperties.Contains(tracked, StringComparer.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Vytvoří doménovou událost na základě entry a definice.
    /// </summary>
    /// <param name="entry">Entry entity</param>
    /// <param name="definition">Definice události</param>
    /// <returns>Doménová událost</returns>
    private static EntityChangedEvent? CreateDomainEvent(EntityEntry entry, DomainEventDefinition definition)
    {
        var entityId = GetEntityId(entry);
        var operation = GetEntityOperation(entry.State);

        return operation switch
        {
            EventType.Created => EntityChangedEvent.Created(definition.EventName, entry.Entity, entityId),
            EventType.Updated => CreateUpdatedEvent(entry, definition),
            EventType.Deleted => EntityChangedEvent.Deleted(definition.EventName, entry.Entity, entityId),
            _ => null
        };
    }

    /// <summary>
    /// Vytvoří událost pro aktualizaci entity.
    /// </summary>
    /// <param name="entry">Entry entity</param>
    /// <param name="definition">Definice události</param>
    /// <returns>Událost aktualizace</returns>
    private static EntityChangedEvent CreateUpdatedEvent(EntityEntry entry, DomainEventDefinition definition)
    {
        var changedProperties = GetChangedProperties(entry);
        var originalValues = GetOriginalValues(entry, changedProperties);
        var currentValues = GetCurrentValues(entry, changedProperties);
        var entityId = GetEntityId(entry);

        return EntityChangedEvent.Updated(
            definition.EventName,
            entry.Entity,
            changedProperties,
            originalValues,
            currentValues,
            entityId);
    }

    /// <summary>
    /// Získá seznam změněných vlastností.
    /// </summary>
    /// <param name="entry">Entry entity</param>
    /// <returns>Seznam názvů změněných vlastností</returns>
    private static List<string> GetChangedProperties(EntityEntry entry)
    {
        return entry.Properties
            .Where(p => p.IsModified)
            .Select(p => p.Metadata.Name)
            .ToList();
    }

    /// <summary>
    /// Získá původní hodnoty vlastností.
    /// </summary>
    /// <param name="entry">Entry entity</param>
    /// <param name="propertyNames">Názvy vlastností</param>
    /// <returns>Slovník původních hodnot</returns>
    private static Dictionary<string, object?> GetOriginalValues(EntityEntry entry, IEnumerable<string> propertyNames)
    {
        var values = new Dictionary<string, object?>();
        foreach (var propertyName in propertyNames)
        {
            var property = entry.Property(propertyName);
            values[propertyName] = property.OriginalValue;
        }
        return values;
    }

    /// <summary>
    /// Získá aktuální hodnoty vlastností.
    /// </summary>
    /// <param name="entry">Entry entity</param>
    /// <param name="propertyNames">Názvy vlastností</param>
    /// <returns>Slovník aktuálních hodnot</returns>
    private static Dictionary<string, object?> GetCurrentValues(EntityEntry entry, IEnumerable<string> propertyNames)
    {
        var values = new Dictionary<string, object?>();
        foreach (var propertyName in propertyNames)
        {
            var property = entry.Property(propertyName);
            values[propertyName] = property.CurrentValue;
        }
        return values;
    }

    /// <summary>
    /// Získá identifikátor entity.
    /// </summary>
    /// <param name="entry">Entry entity</param>
    /// <returns>Identifikátor entity</returns>
    private static object? GetEntityId(EntityEntry entry)
    {
        var keyProperties = entry.Metadata.FindPrimaryKey()?.Properties;
        if (keyProperties?.Count == 1)
        {
            return entry.Property(keyProperties.First().Name).CurrentValue;
        }
        return null;
    }

    /// <summary>
    /// Přidá doménovou událost k entitě (pokud entita podporuje doménové události).
    /// </summary>
    /// <param name="entity">Entita</param>
    /// <param name="domainEvent">Doménová událost</param>
    private static void AddDomainEventToEntity(object entity, DomainEvent domainEvent)
    {
        // Zkusíme najít metodu AddDomainEvent pomocí reflexe
        var entityType = entity.GetType();
        var addDomainEventMethod = entityType.GetMethod("AddDomainEvent", new[] { typeof(DomainEvent) });

        if (addDomainEventMethod != null)
        {
            addDomainEventMethod.Invoke(entity, new object[] { domainEvent });
        }
    }
}
