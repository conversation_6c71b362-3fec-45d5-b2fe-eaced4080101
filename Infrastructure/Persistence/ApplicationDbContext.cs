using System.Linq.Expressions;
using ExpressionEntity = Infrastructure.ExpressionEngine.Expression;
using Application.Abstraction;
using Application.Services.Events;
using Domain.Identity;
using Domain.System;
using Infrastructure.Extensions;
using Infrastructure.Identity;
using Infrastructure.ExpressionEngine;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using SharedKernel.Domain;

namespace Infrastructure.Persistence;

/// <summary>
/// Hlavní databázový kontext aplikace, který implementuje rozhraní IApplicationDbContext.
/// Rozšiřuje IdentityDbContext pro podporu uživatelských účtů a rolí.
/// Zajišťuje přístup k databázi a implementuje business logiku pro ukládání dat,
/// včetně auditování, soft delete a publikování doménových událostí.
/// </summary>
public class ApplicationDbContext : IdentityDbContext<ApplicationUser, IdentityRole<int>, int>, IApplicationDbContext
{
    /// <summary>
    /// Služba pro získání informací o aktuálním uživateli.
    /// Používá se pro automatické vyplnění auditovacích polí.
    /// </summary>
    private readonly ICurrentUserService _currentUserService;

    /// <summary>
    /// Služba pro publikování doménových událostí.
    /// Používá se pro odesílání událostí po úspěšném uložení změn.
    /// </summary>
    private readonly DomainEventPublisher _domainEventPublisher;

    /// <summary>
    /// Inicializuje novou instanci třídy ApplicationDbContext.
    /// </summary>
    /// <param name="options">Konfigurace databázového kontextu</param>
    /// <param name="currentUserService">Služba pro získání informací o aktuálním uživateli</param>
    /// <param name="domainEventPublisher">Služba pro publikování doménových událostí</param>
    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        ICurrentUserService currentUserService,
        DomainEventPublisher domainEventPublisher)
        : base(options)
    {
        _currentUserService = currentUserService;
        _domainEventPublisher = domainEventPublisher;
    }

    // DbSet vlastnosti se automaticky vytváří na základě registrovaných entit v OnModelCreating
    // Systémové entity - explicitně definované pro jasnost

    /// <summary>
    /// Kolekce záznamů o auditování změn v databázi.
    /// </summary>
    public DbSet<AuditTrail> AuditTrails { get; set; }

    /// <summary>
    /// Kolekce uživatelských profilů.
    /// </summary>
    public DbSet<UserProfile> UserProfiles { get; set; }

    /// <summary>
    /// Kolekce systémových logů aplikace.
    /// </summary>
    public DbSet<SystemLog> SystemLogs { get; set; }

    /// <summary>
    /// Kolekce výrazů pro výpočty a validace.
    /// </summary>
    public DbSet<ExpressionEntity> Expressions { get; set; }

    /// <summary>
    /// Konfiguruje model databáze při jeho vytváření.
    /// </summary>
    /// <param name="modelBuilder">Builder pro konfiguraci modelu databáze</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Nejprve zavoláme základní implementaci, která nakonfiguruje Identity tabulky
        base.OnModelCreating(modelBuilder);

        // Automaticky registrujeme všechny obchodní entity
        RegisterBusinessEntitiesAutomatically(modelBuilder);

        // Aplikujeme obecnou konfiguraci pro všechny entity ze SharedKernel
        ConfigureSharedKernelEntities(modelBuilder);

        // Aplikujeme všechny konfigurace entit z namespace Infrastructure.Persistence.Configurations
        // Toto automaticky najde a použije všechny třídy implementující IEntityTypeConfiguration<T>
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

        // Aplikace globálního filtru pro všechny entity implementující ISoftDelete
        // Tento filtr automaticky vyloučí z výsledků dotazů všechny entity, které byly označeny jako smazané
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(ISoftDelete).IsAssignableFrom(entityType.ClrType))
            {
                // Vytvoříme dynamický LINQ výraz pomocí Expression API
                var parameter = System.Linq.Expressions.Expression.Parameter(entityType.ClrType, "e");                      // Parametr 'e' reprezentující entitu
                var property = System.Linq.Expressions.Expression.Property(parameter, nameof(ISoftDelete.DeletedAt));         // Přístup k vlastnosti Deleted
                var nullCheck = System.Linq.Expressions.Expression.Equal(property, System.Linq.Expressions.Expression.Constant(null));              // Kontrola, zda je Deleted == null
                var lambda = System.Linq.Expressions.Expression.Lambda(nullCheck, parameter);                               // Vytvoření lambda výrazu

                // Aplikace filtru na entitu - vrátí pouze záznamy, kde Deleted == null (tzn. nebyly smazány)
                modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
            }
        }
    }

    /// <summary>
    /// Přepisuje standardní metodu SaveChangesAsync pro přidání vlastní business logiky
    /// při ukládání změn do databáze.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet záznamů, které byly uloženy do databáze</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Poznámka: Tracking polí (CreatedAt, CreatedBy, ModifiedAt, ModifiedBy) je nyní zajišťován
        // TrackableEntityInterceptor, takže zde není potřeba žádná logika pro ITrackableEntity

        // Automatické vyplnění RowVersion pro entity implementující BaseEntity
        // SQLite nepodporuje automatické generování timestamp hodnot, takže je musíme nastavit manuálně
        foreach (var entry in ChangeTracker.Entries())
        {
            // Kontrolujeme, zda entita dědí z BaseEntity (bez ohledu na generický parametr)
            var entityType = entry.Entity.GetType();
            var baseEntityType = entityType.BaseType;

            while (baseEntityType != null)
            {
                if (baseEntityType.IsGenericType &&
                    baseEntityType.GetGenericTypeDefinition() == typeof(BaseEntity<>))
                {
                    // Našli jsme BaseEntity<T>, nyní získáme RowVersion property
                    var rowVersionProperty = entityType.GetProperty("RowVersion");
                    if (rowVersionProperty != null)
                    {
                        var currentRowVersion = (byte[]?)rowVersionProperty.GetValue(entry.Entity);

                        switch (entry.State)
                        {
                            case EntityState.Added:
                                // Pro nové entity nastavíme výchozí RowVersion
                                if (currentRowVersion == null || currentRowVersion.Length == 0)
                                {
                                    rowVersionProperty.SetValue(entry.Entity, new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 });
                                }
                                break;
                            case EntityState.Modified:
                                // Pro modifikované entity zvýšíme RowVersion
                                if (currentRowVersion != null && currentRowVersion.Length == 8)
                                {
                                    var version = BitConverter.ToInt64(currentRowVersion, 0);
                                    rowVersionProperty.SetValue(entry.Entity, BitConverter.GetBytes(version + 1));
                                }
                                else
                                {
                                    rowVersionProperty.SetValue(entry.Entity, new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 });
                                }
                                break;
                        }
                    }
                    break; // Našli jsme BaseEntity, můžeme ukončit hledání
                }
                baseEntityType = baseEntityType.BaseType;
            }
        }

        // Implementace soft delete pro entity implementující ISoftDelete
        // Místo fyzického smazání záznamů z databáze pouze označíme záznam jako smazaný
        var now = DateTime.UtcNow;
        var currentUser = _currentUserService.UserId ?? "anonymous";

        foreach (var entry in ChangeTracker.Entries<ISoftDelete>())
        {
            if (entry.State == EntityState.Deleted)
            {
                // Změníme stav entity z Deleted na Modified, aby nedošlo k fyzickému smazání
                entry.State = EntityState.Modified;
                // Nastavíme čas smazání a uživatele, který entitu smazal
                entry.Entity.DeletedAt = now;
                entry.Entity.DeletedBy = currentUser;
            }
        }

        // Získání doménových událostí před uložením změn
        // Tyto události budou publikovány až po úspěšném uložení změn do databáze
        var entitiesWithEvents = ChangeTracker.Entries<BaseEntity<object>>()
            .Where(e => e.Entity.DomainEvents.Any())  // Vybereme pouze entity, které mají nějaké doménové události
            .Select(e => e.Entity)
            .ToList();

        // Získáme všechny doménové události ze všech entit
        var domainEvents = entitiesWithEvents
            .SelectMany(e => e.DomainEvents)
            .ToList();

        // Vyčistíme doménové události z entit, aby nebyly publikovány vícekrát
        entitiesWithEvents.ForEach(entity => entity.ClearDomainEvents());

        // Uložíme změny do databáze pomocí základní implementace SaveChangesAsync
        var result = await base.SaveChangesAsync(cancellationToken);

        // Publikování doménových událostí po úspěšném uložení změn
        // Toto umožňuje dalším částem aplikace reagovat na změny v databázi
        foreach (var domainEvent in domainEvents)
        {
            await _domainEventPublisher.Publish(domainEvent);
        }

        return result;
    }

    /// <summary>
    /// Automaticky registruje všechny obchodní entity z Domain vrstvy.
    /// Najde všechny třídy implementující IEntity a registruje je do modelu.
    /// </summary>
    /// <param name="modelBuilder">Builder pro konfiguraci modelu databáze</param>
    private void RegisterBusinessEntitiesAutomatically(ModelBuilder modelBuilder)
    {
        // Získáme všechny typy z Domain assembly (ne SharedKernel)
        var domainAssembly = typeof(Domain.Entities.SampleEntity).Assembly;

        var entityTypes = domainAssembly.GetTypes()
            .Where(t => t.IsClass &&
                       !t.IsAbstract &&
                       typeof(SharedKernel.Domain.IEntity).IsAssignableFrom(t) &&
                       !IsSystemEntity(t) &&
                       !IsDomainEventType(t)) // Vyloučíme doménové události
            .ToList();

        // Registrujeme každou nalezenou entitu
        foreach (var entityType in entityTypes)
        {
            modelBuilder.Entity(entityType);
        }
    }

    /// <summary>
    /// Určuje, zda je entita systémová (má explicitní DbSet vlastnost).
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <returns>True pokud je systémová entita</returns>
    private static bool IsSystemEntity(Type entityType)
    {
        // Systémové entity, které mají explicitní DbSet vlastnosti
        var systemEntityTypes = new[]
        {
            typeof(AuditTrail),
            typeof(UserProfile),
            typeof(SystemLog),
            typeof(ExpressionEntity)
        };

        return systemEntityTypes.Contains(entityType);
    }

    /// <summary>
    /// Určuje, zda je typ doménová událost, která se neměla by registrovat jako entita.
    /// </summary>
    /// <param name="entityType">Typ k ověření</param>
    /// <returns>True pokud je doménová událost</returns>
    private static bool IsDomainEventType(Type entityType)
    {
        // Doménové události dědí z DomainEvent a neměly by být registrovány jako entity
        return typeof(DomainEvent).IsAssignableFrom(entityType);
    }

    /// <summary>
    /// Konfiguruje obecné vlastnosti pro všechny entity dědící ze SharedKernel base tříd.
    /// Automaticky nakonfiguruje optimistické zamykání a vyloučí DomainEvents z mapování.
    /// </summary>
    /// <param name="modelBuilder">Builder pro konfiguraci modelu databáze</param>
    private void ConfigureSharedKernelEntities(ModelBuilder modelBuilder)
    {
        // Najdeme všechny entity typy v modelu
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var clrType = entityType.ClrType;

            // Zkontrolujeme, zda entita implementuje IEntity<T> ze SharedKernel
            var entityInterface = clrType.GetInterfaces()
                .FirstOrDefault(i => i.IsGenericType &&
                               i.GetGenericTypeDefinition() == typeof(SharedKernel.Domain.IEntity<>));

            if (entityInterface != null)
            {
                // Konfigurace RowVersion pro optimistické zamykání
                var rowVersionProperty = entityType.FindProperty("RowVersion");
                if (rowVersionProperty != null)
                {
                    modelBuilder.Entity(clrType)
                        .Property("RowVersion")
                        .IsRowVersion()
                        .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 })
                        .HasComment("Verze řádku pro optimistické zamykání");
                }
            }

            // Zkontrolujeme, zda entita dědí z BaseEntity<T> a má DomainEvents
            if (clrType.IsAssignableFromGeneric(typeof(SharedKernel.Domain.BaseEntity<>)))
            {
                // Vyloučíme DomainEvents z mapování do databáze
                modelBuilder.Entity(clrType)
                    .Ignore("DomainEvents");
            }
        }
    }
}
