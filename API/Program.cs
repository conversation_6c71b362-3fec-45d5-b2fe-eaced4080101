using API;
using Application;
using Infrastructure;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Identity.Web;

var builder = WebApplication.CreateBuilder(args);

// === Registrace služeb ===
// API vrstva - endpointy, swagger, JSON konfigurace
builder.Services.AddApiServices();

// Autentifikace a autorizace (pouze pro produkci)
if (!builder.Environment.IsDevelopment())
{
    builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddMicrosoftIdentityWebApi(builder.Configuration, "AzureAd");
    builder.Services.AddAuthorization();
}

// Ostatní vrstvy
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructureServices(builder.Configuration);

var app = builder.Build();

// === Konfigurace middleware pipeline ===
// HTTPS redirection (globální bezpečnost)
app.UseHttpsRedirection();

// Autentifikace a autorizace (pouze pro produkci)
if (!app.Environment.IsDevelopment())
{
    app.UseAuthentication();
    app.UseAuthorization();
}

// API konfigurace (endpointy, swagger, atd.)
app.UseApiConfiguration();

app.Run();


// Umožňuje přístup k Program třídě pro testování
namespace API
{
    public partial class Program { }
}
