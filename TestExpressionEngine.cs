using System;
using System.Threading.Tasks;
using Domain.Entities;
using Infrastructure.ExpressionEngine;
using Infrastructure.ExpressionEngine.Services;

namespace TestExpressionEngine
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Test ExpressionEngine ===");

            // Vytvoření testovací entity
            var sampleEntity = new SampleEntity
            {
                Id = 1,
                Name = "Test Entity",
                Age = 25,
                IsActive = true,
                DateOfBirth = new DateTime(1998, 5, 15)
            };

            // Vytvoření jednoduchého výrazu: Age + 10
            var expression = new Expression
            {
                Id = Guid.NewGuid(),
                Name = "Add Ten to Age",
                Description = "Přidá 10 k věku entity",
                TargetEntityName = "SampleEntity",
                SchemaVersion = "1.0",
                RootNode = new OperationNode
                {
                    Operator = OperatorType.Add,
                    Operands = new List<ExpressionNode>
                    {
                        new SourceValueNode { SourcePath = "Age" },
                        new ConstantNode { DataType = ValueType.Integer, Value = "10" }
                    }
                }
            };

            Console.WriteLine($"Testovací entita: {sampleEntity.Name}, Věk: {sampleEntity.Age}");
            Console.WriteLine($"Výraz: {expression.Name} - {expression.Description}");

            try
            {
                // Vytvoření CalculationEngine (bez DI pro jednoduchost)
                var cacheService = new ExpressionCacheService();
                var logger = new ConsoleLogger();
                var engine = new CalculationEngine(cacheService, logger);

                // Spuštění výrazu
                var result = await engine.ExecuteAsync(expression, sampleEntity);
                
                Console.WriteLine($"Výsledek: {result}");
                Console.WriteLine($"Očekávaný výsledek: {sampleEntity.Age + 10}");
                
                if (result.Equals(sampleEntity.Age + 10))
                {
                    Console.WriteLine("✅ Test prošel úspěšně!");
                }
                else
                {
                    Console.WriteLine("❌ Test selhal!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Chyba při testování: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\nStiskněte libovolnou klávesu pro ukončení...");
            Console.ReadKey();
        }
    }

    // Jednoduchá implementace cache pro test
    public class ExpressionCacheService : IExpressionCacheService
    {
        private readonly Dictionary<string, object> _cache = new();

        public Task<T> GetOrSetAsync<T>(string key, Func<T> factory, TimeSpan expiration)
        {
            if (_cache.TryGetValue(key, out var cached))
            {
                return Task.FromResult((T)cached);
            }

            var value = factory();
            _cache[key] = value;
            return Task.FromResult(value);
        }

        public Task RemoveAsync(string key)
        {
            _cache.Remove(key);
            return Task.CompletedTask;
        }

        public Task ClearAsync()
        {
            _cache.Clear();
            return Task.CompletedTask;
        }
    }

    // Jednoduchá implementace loggeru pro test
    public class ConsoleLogger : Microsoft.Extensions.Logging.ILogger<CalculationEngine>
    {
        public IDisposable BeginScope<TState>(TState state) => null;
        public bool IsEnabled(Microsoft.Extensions.Logging.LogLevel logLevel) => true;

        public void Log<TState>(Microsoft.Extensions.Logging.LogLevel logLevel, Microsoft.Extensions.Logging.EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            Console.WriteLine($"[{logLevel}] {formatter(state, exception)}");
        }
    }
}
